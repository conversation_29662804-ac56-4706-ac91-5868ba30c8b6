import discord
from discord.ext import commands
import random
from datetime import datetime, timedelta
import sqlite3
from discord import app_commands
from discord import Embed, Color, SelectOption
from typing import Optional, Literal
from discord.ui import View, Button, Select, Modal, TextInput
import json
import time
from contextlib import contextmanager
import os
import shutil
from discord.ext import tasks
import asyncio
import re
import logging
import functools
import csv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("bank_bot.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("BankBot")

# Rate Limiter class to handle Discord API rate limits
class RateLimiter:
    def __init__(self):
        self.rate_limits = {}
        self.global_rate_limit = None
        self.global_rate_limit_reset = None
        
    async def execute_with_rate_limit(self, endpoint, func, *args, **kwargs):
        """Execute a Discord API function with rate limit handling and exponential backoff"""
        max_retries = 5
        base_delay = 0.5
        
        # Check if we're under global rate limit
        if self.global_rate_limit and time.time() < self.global_rate_limit_reset:
            wait_time = self.global_rate_limit_reset - time.time()
            logger.warning(f"Global rate limit in effect. Waiting {wait_time:.2f} seconds")
            await asyncio.sleep(wait_time)
        
        # Check if this specific endpoint is rate limited
        if endpoint in self.rate_limits and time.time() < self.rate_limits[endpoint]["reset"]:
            wait_time = self.rate_limits[endpoint]["reset"] - time.time()
            logger.warning(f"Endpoint {endpoint} rate limited. Waiting {wait_time:.2f} seconds")
            await asyncio.sleep(wait_time)
        
        # Try the request with exponential backoff
        for attempt in range(max_retries):
            try:
                return await func(*args, **kwargs)
            except discord.errors.HTTPException as e:
                if e.status == 429:  # Rate limit error
                    retry_after = e.retry_after
                    
                    # Check if it's a global rate limit
                    if getattr(e, 'global', False):
                        self.global_rate_limit = True
                        self.global_rate_limit_reset = time.time() + retry_after
                        logger.warning(f"Hit global rate limit! Retry after {retry_after:.2f}s")
                    else:
                        # Endpoint-specific rate limit
                        self.rate_limits[endpoint] = {
                            "reset": time.time() + retry_after,
                            "limit": getattr(e, 'limit', None)
                        }
                        logger.warning(f"Rate limited on {endpoint}! Retry after {retry_after:.2f}s")
                    
                    # Wait the time Discord told us to wait
                    await asyncio.sleep(retry_after)
                elif attempt < max_retries - 1:
                    # For other HTTP errors, use exponential backoff
                    backoff_time = base_delay * (2 ** attempt)
                    logger.warning(f"Request failed with {e}. Retrying in {backoff_time:.2f}s (attempt {attempt+1}/{max_retries})")
                    await asyncio.sleep(backoff_time)
                else:
                    # Final attempt failed
                    logger.error(f"Failed after {max_retries} attempts: {e}")
                    raise
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                raise
        
        # This should never be reached, but just in case
        raise RuntimeError("Failed to execute API call after retries")

# Command cooldown manager for better rate limiting of commands
class CommandCooldownManager:
    def __init__(self):
        self.cooldowns = {}
        self.bucket_types = {
            "user": lambda interaction: str(interaction.user.id),
            "guild": lambda interaction: str(interaction.guild_id) if interaction.guild else "DM",
            "channel": lambda interaction: str(interaction.channel_id),
            "global": lambda _: "global"
        }
    
    def is_on_cooldown(self, command_name, interaction, bucket_type="user"):
        """Check if a command is on cooldown"""
        bucket_key = self.bucket_types[bucket_type](interaction)
        cooldown_key = f"{command_name}:{bucket_key}"
        
        if cooldown_key in self.cooldowns:
            expiry = self.cooldowns[cooldown_key]["expiry"]
            if time.time() < expiry:
                return True, self.cooldowns[cooldown_key]["expiry"] - time.time()
        
        return False, 0
    
    def set_cooldown(self, command_name, interaction, cooldown_seconds, bucket_type="user"):
        """Put a command on cooldown"""
        bucket_key = self.bucket_types[bucket_type](interaction)
        cooldown_key = f"{command_name}:{bucket_key}"
        
        self.cooldowns[cooldown_key] = {
            "expiry": time.time() + cooldown_seconds,
            "set_at": time.time()
        }
    
    def reset_cooldown(self, command_name, interaction, bucket_type="user"):
        """Reset a command's cooldown"""
        bucket_key = self.bucket_types[bucket_type](interaction)
        cooldown_key = f"{command_name}:{bucket_key}"
        
        if cooldown_key in self.cooldowns:
            del self.cooldowns[cooldown_key]
    
    def format_remaining_time(self, seconds):
        """Format remaining cooldown time into a human-readable string"""
        if seconds < 60:
            return f"{int(seconds)} seconds"
        
        minutes, seconds = divmod(int(seconds), 60)
        if minutes < 60:
            return f"{minutes} minutes and {seconds} seconds"
        
        hours, minutes = divmod(minutes, 60)
        return f"{hours} hours, {minutes} minutes and {seconds} seconds"
    
    def cleanup_expired(self):
        """Remove expired cooldowns to prevent memory leaks"""
        now = time.time()
        expired_keys = []
        
        for key, data in self.cooldowns.items():
            # Skip work and income related cooldowns
            if key.startswith("work:") or key.startswith("collect_income:"):
                continue
                
            if data["expiry"] < now:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cooldowns[key]
            
        logger.debug(f"Cleaned up {len(expired_keys)} expired cooldowns. {len(self.cooldowns)} active cooldowns remain.")

# Initialize rate limiter
rate_limiter = RateLimiter()

# Initialize command cooldown manager
cooldown_manager = CommandCooldownManager()

# Bot setup
intents = discord.Intents.default()
intents.message_content = True
intents.members = True
intents.guilds = True
bot = commands.Bot(command_prefix='/', intents=intents)

# Helper functions for rate-limited API calls
async def send_message(interaction, content=None, embed=None, view=None, ephemeral=False):
    """Safely send a message with rate limit handling"""
    endpoint = "interaction.response.send_message"
    
    # Create kwargs with non-None parameters only
    kwargs = {}
    if content is not None:
        kwargs['content'] = content
    if embed is not None:
        kwargs['embed'] = embed
    if view is not None:
        kwargs['view'] = view
    kwargs['ephemeral'] = ephemeral
    
    if not interaction.response.is_done():
        return await rate_limiter.execute_with_rate_limit(
            endpoint,
            interaction.response.send_message,
            **kwargs
        )
    else:
        # Use followup instead
        endpoint = "interaction.followup.send"
        return await rate_limiter.execute_with_rate_limit(
            endpoint,
            interaction.followup.send,
            **kwargs
        )

async def edit_message(interaction, content=None, embed=None, view=None):
    """Safely edit a message with rate limit handling"""
    endpoint = "interaction.response.edit_message"
    
    # Create kwargs with non-None parameters only
    kwargs = {}
    if content is not None:
        kwargs['content'] = content
    if embed is not None:
        kwargs['embed'] = embed
    if view is not None:
        kwargs['view'] = view
    
    return await rate_limiter.execute_with_rate_limit(
        endpoint,
        interaction.response.edit_message,
        **kwargs
    )

async def defer_response(interaction, ephemeral=False):
    """Safely defer a response with rate limit handling"""
    endpoint = "interaction.response.defer"
    
    return await rate_limiter.execute_with_rate_limit(
        endpoint,
        interaction.response.defer,
        ephemeral=ephemeral
    )

# Store user balances and cooldowns
class BankSystem:
    def __init__(self):
        # Set database connection parameters first
        self.max_retries = 5
        self.retry_delay = 0.1
        
        # Then load config
        with open('config.json') as f:
            self.config = json.load(f)
        
        self.currency_emoji = self.config['currency_emoji']
        self.banker_roles = self.config['banker_roles']
        self.default_work = self.config['default_work']
        self.house_emoji = self.config['house_emoji']
        
        self.work_emoji = "💼"
        self.bank_emoji = "🏦"
        self.time_emoji = "⏳"
        self.vip_emoji = "⭐"
        self.premium_vip_emoji = "🌟"  # Premium star for quarterly VIP members
        
        self.success_color = Color.green()
        self.error_color = Color.red()
        self.info_color = Color.blue()
        self.vip_color = Color.gold()
        
        # Currency conversion rates
        self.SICKLES_PER_GALLEON = 17
        self.KNUTS_PER_SICKLE = 29
        self.KNUTS_PER_GALLEON = 493  # 17 * 29
        
        # VIP membership prices (in Knuts)
        self.VIP_WEEKLY_PRICE = 3 * self.KNUTS_PER_GALLEON  # 3 Galleons
        self.VIP_MONTHLY_PRICE = 10 * self.KNUTS_PER_GALLEON  # 10 Galleons
        self.VIP_QUARTERLY_PRICE = 25 * self.KNUTS_PER_GALLEON  # 25 Galleons
        
        # VIP tiers
        self.VIP_TIERS = {
            "weekly": {
                "name": "Weekly VIP",
                "price": self.VIP_WEEKLY_PRICE,
                "duration": 7  # days
            },
            "monthly": {
                "name": "Monthly VIP",
                "price": self.VIP_MONTHLY_PRICE,
                "duration": 30  # days
            },
            "quarterly": {
                "name": "Quarterly VIP",
                "price": self.VIP_QUARTERLY_PRICE,
                "duration": 90  # days
            }
        }
        
        # Initialize database
        self.init_database()
        
        # Add work quotes
        self.work_quotes = [
            "قمت بتوصيل عدد المتنبئ اليومي للعالم السحري نيابةً عن البومة الخاصة بالآنسة رولا، فكافأتك بـ",
            "أرسلتك الآنسة رولا ويزلي في رحلة للبحث عن قطتها المفقودة، فلم تجدها، لكنها كافأتك ببعض الحلوى و",
            "شاركت في إحدى مسابقات مكتب الألعاب، وأسفرت تعويذتك عن نشوب فوضى طريفة بين الطلاب، فمنحتك الآنسة ليان بلاك",
            "بينما كنت تساعد كائنًا مصابًا قُرب الغابة المحرمة، لفت انتباهك غرابٌ غريب، ثم سمعت فجأة خطواتٍ غامضة، لتجد بروفيسور دارك زولديك خلفك يبتسم بلطف ويساعدك، ثم يكافأك ببعض الكنوتات، محذرًا إياك من العودة إلى الغابة.",
            "أثناء تقديم الطعام لـ سياه، قرر أن الطعام أقل أهمية من اختبار سرعتك في الجري، فعوضتك الآنسة ليان بـ",
            "قمت بمساعدة قسم الإصلاح في عرض الطقس على الشاشة، فكافأك السيد وزا زولديك بـ",
            "قمت بالإبلاغ عن عطل فني، فكافأك السيد وزا زولديك بـ",
            "ذكّرت أصدقاءك ببعض القوانين الخاصة بإلقاء التعاويذ في الحانات والنوادي، فكافأك بروفيسور فيدريكو بـ",
            "قمت بلقاء صحفي رائع مع السيد لويس بانر ونالت الأسئلة إعجابه، فأعطاك",
            "ساعدت السيد لويس بانر في ترتيب مكتبه بوزارة السحر، فأعطاك",
            "ساعدت بعض الأورورز في القبض على سارق في حارة دياجون، فأعطاك السيد فيدريكو بلاكفاي",
            "قمت بمساعدة بروفيسور ريتشارد هاجريد في إطعام بعض المخلوقات السحرية، فكافأك بـ",
            "حضّرت وصفة الحظ السائل بإتقان، فكافأك بروفيسور زينو دمبلدور بـ",
            "وجدت بعضًا من جذور الماندريك وقدمتها لبروفيسور زينو دمبلدور، فأعطاك",
            "اقترحت فكرة رائعة ومبتكرة على السيد چوناه إرنست، فكافأك بـ",
            "كشفت مخطط مجموعة من اللصوص الذين يخططون لسرقة بعض الأدوات السحرية المحظورة وأبلغت عنهم، فكافأك السيد فيدريكو بلاكفاير بـ",
            "ساعدت السيد چيمس دوم في تصنيف بعض السحرة في أشجار عائلاتهم السحرية، فمنحك",
            "عَلِم السيد ديلان ديسانتا أنك مرضت وبقيت الليلة الماضية عند مدام بومفري، فأرسل لك حلوى بيرتي بوتس ومنحك ",
            "رآك بروفيسور ديلان تعتني بشجرة الصفصاف رغم خطورتها، فمنحك بقشيشًا، وقال لك بجدية:``عمل جيد ولكن الخروج من القلعة دون إذن غير مقبول، اذهب لغرفة العقاب حالًا.`` ",
            "قمت بترتيب السِّيَر ااذاتية للمتقدمين لوظائف مكتب التوظيف، فقامت الآنسة روز ويزلي بمنحك",
            "ساعدت أحد أقسام الوزارة في استعادة ملفات اختفت بفعل تعويذة فوضوية، فبعث لك الوزير بانر مذكرة شكر بخط يده، ومعها"

        ]
        
        self.log_channel_id = self.config['log_channel_id']

    def init_database(self):
        with self.get_db_connection() as conn:
            c = conn.cursor()
            try:
                # Enable foreign key support
                c.execute('PRAGMA foreign_keys = ON')
                c.execute('BEGIN TRANSACTION')
                
                # Create version table first if doesn't exist
                c.execute('''CREATE TABLE IF NOT EXISTS db_version
                            (version INTEGER PRIMARY KEY)''')
                            
                # Check current version
                try:
                    c.execute('SELECT version FROM db_version')
                    result = c.fetchone()
                    current_version = result[0] if result else 0
                except sqlite3.OperationalError:
                    # db_version table doesn't exist, this is a fresh database
                    current_version = 0
                
                # Run migrations based on current version
                self._run_migrations(c, current_version)
                
                conn.commit()
                print("Database initialization complete!")
                
            except Exception as e:
                conn.rollback()
                print(f"Error initializing database: {e}")
                raise
    
    def _run_migrations(self, cursor, current_version):
        """Run database migrations sequentially based on current version."""
        print(f"Current database version: {current_version}")

        # If current_version is 0, we need to detect if this is a fresh database or existing
        if current_version == 0:
            # Check if main tables exist to determine if this is an existing database
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
            if cursor.fetchone():
                print("Detected existing database without version tracking, setting to version 7")
                current_version = 7  # Assume it has all tables up to VIP system
                cursor.execute('''CREATE TABLE IF NOT EXISTS db_version
                                (version INTEGER PRIMARY KEY)''')
                cursor.execute('INSERT INTO db_version (version) VALUES (7)')

        # Version 1-5: Base tables
        if current_version < 5:
            print("Running migration to version 5: Creating base tables...")

            # Create cooldowns table
            cursor.execute('''CREATE TABLE IF NOT EXISTS cooldowns
                            (user_id TEXT PRIMARY KEY,
                             work_cooldown TEXT,
                             income_cooldown TEXT)''')

            # Create transactions table
            cursor.execute('''CREATE TABLE IF NOT EXISTS transactions
                            (id INTEGER PRIMARY KEY AUTOINCREMENT,
                             user_id TEXT,
                             amount INTEGER,
                             type TEXT,
                             timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                             modifier_id TEXT,
                             details TEXT)''')

            # Create tables in proper order (dependencies first)
            cursor.execute('''CREATE TABLE IF NOT EXISTS users
                            (user_id TEXT PRIMARY KEY,
                             username TEXT,
                             galleons INTEGER DEFAULT 0,
                             sickles INTEGER DEFAULT 0,
                             knuts INTEGER DEFAULT 0,
                             favorite_spells TEXT DEFAULT '',
                             pets TEXT DEFAULT '',
                             bio TEXT DEFAULT '')''')

            cursor.execute('''CREATE TABLE IF NOT EXISTS shop_items
                            (id INTEGER PRIMARY KEY AUTOINCREMENT,
                             name TEXT NOT NULL,
                             price INTEGER NOT NULL,
                             category TEXT NOT NULL,
                             description TEXT,
                             properties TEXT,
                             required_role TEXT,
                             added_by TEXT NOT NULL,
                             added_timestamp TEXT DEFAULT CURRENT_TIMESTAMP)''')

            cursor.execute('''CREATE TABLE IF NOT EXISTS removed_shop_items
                            (id INTEGER PRIMARY KEY,
                             name TEXT NOT NULL,
                             price INTEGER NOT NULL,
                             category TEXT NOT NULL,
                             description TEXT,
                             properties TEXT,
                             added_by TEXT NOT NULL,
                             removed_timestamp TEXT DEFAULT CURRENT_TIMESTAMP)''')

            cursor.execute('''CREATE TABLE IF NOT EXISTS inventory
                            (id INTEGER PRIMARY KEY AUTOINCREMENT,
                             user_id TEXT NOT NULL,
                             item_id INTEGER NOT NULL,
                             properties TEXT,
                             obtained_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                             is_removed_item BOOLEAN DEFAULT 0,
                             category TEXT,
                             FOREIGN KEY(user_id) REFERENCES users(user_id))''')

            # Add default items
            try:
                # Add default wand if none exists
                cursor.execute('SELECT 1 FROM shop_items WHERE category = "Wands" LIMIT 1')
                if not cursor.fetchone():
                    cursor.execute('''INSERT INTO shop_items
                                    (name, price, category, description, properties, added_by)
                                    VALUES (?, ?, ?, ?, ?, ?)''',
                                 ("Training Wand",
                                  1 * self.KNUTS_PER_GALLEON,  # 1 Galleons
                                  "Wands",
                                  "A basic training wand for new students",
                                  json.dumps({
                                      "wood": "Cherry",
                                      "core": "Unicorn Hair",
                                      "length": 8.75,
                                      "flexibility": "Slightly Springy",
                                      "power": "0"
                                  }),
                                  "SYSTEM"))
                    print("Added default wand")

                # Add default broom if none exists
                cursor.execute('SELECT 1 FROM shop_items WHERE category = "Brooms" LIMIT 1')
                if not cursor.fetchone():
                    cursor.execute('''INSERT INTO shop_items
                                    (name, price, category, description, properties, added_by)
                                    VALUES (?, ?, ?, ?, ?, ?)''',
                                 ("Training Broom",
                                  1 * self.KNUTS_PER_GALLEON,  # 1 Galloen
                                  "Brooms",
                                  "A reliable training broom for beginners",
                                  json.dumps({
                                      "wood": "Birch",
                                      "bristle": "Twiggy Birch",
                                      "length": 48,
                                      "speed": "0"
                                  }),
                                  "SYSTEM"))
                    print("Added default broom")

                # Add default accessory if none exists
                cursor.execute('SELECT 1 FROM shop_items WHERE category = "Accessories" LIMIT 1')
                if not cursor.fetchone():
                    cursor.execute('''INSERT INTO shop_items
                                    (name, price, category, description, properties, added_by)
                                    VALUES (?, ?, ?, ?, ?, ?)''',
                                 ("Basic Necklace",
                                  1 * self.KNUTS_PER_GALLEON, # 1 Galloen
                                  "Accessories",
                                  "A simple magical necklace",
                                  json.dumps({
                                      "material": "Silver",
                                      "type": "Necklace",
                                      "enchantment": "+1 Ac"
                                  }),
                                  "SYSTEM"))
                    print("Added default accessory")
            except Exception as e:
                print(f"Error adding default items: {e}")
                raise

            # Update version
            cursor.execute('UPDATE db_version SET version = 5')
            current_version = 5
        
        # Version 6: Performance optimization
        if current_version < 6:
            print("Running migration to version 6: Adding performance indexes...")
            
            # Add indexes for performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_inventory_user_id ON inventory(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_shop_items_category ON shop_items(category)')
            
            # Add additional indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_timestamp ON transactions(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_cooldowns_work ON cooldowns(work_cooldown)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_cooldowns_income ON cooldowns(income_cooldown)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory(category)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_inventory_item_id ON inventory(item_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_shop_items_name ON shop_items(name)')
            
            # Database optimization settings
            cursor.execute('PRAGMA optimize')
            
            # Update version
            cursor.execute('UPDATE db_version SET version = 6')
            current_version = 6
        
        # Version 7: VIP system
        if current_version < 7:
            print("Running migration to version 7: Adding VIP membership system...")
            
            # Create VIP memberships table
            cursor.execute('''CREATE TABLE IF NOT EXISTS vip_memberships
                            (id INTEGER PRIMARY KEY AUTOINCREMENT,
                             user_id TEXT UNIQUE,
                             tier TEXT NOT NULL,
                             start_date TEXT NOT NULL,
                             expiry_date TEXT NOT NULL,
                             last_work_notification TEXT,
                             last_income_notification TEXT)''')
            
            # Add index for VIP memberships
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_vip_user_id ON vip_memberships(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_vip_expiry_date ON vip_memberships(expiry_date)')
            
            # Update version
            cursor.execute('UPDATE db_version SET version = 7')
            current_version = 7

        # Version 8: Persistent notification system
        if current_version < 8:
            print("Running migration to version 8: Adding persistent notification system...")

            # Create scheduled_notifications table
            cursor.execute('''CREATE TABLE IF NOT EXISTS scheduled_notifications
                            (id INTEGER PRIMARY KEY AUTOINCREMENT,
                             user_id TEXT NOT NULL,
                             notification_type TEXT NOT NULL,
                             scheduled_time TEXT NOT NULL,
                             created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                             sent_at TEXT,
                             status TEXT DEFAULT 'pending')''')

            # Add indexes for performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_user_id ON scheduled_notifications(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_scheduled_time ON scheduled_notifications(scheduled_time)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_status ON scheduled_notifications(status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_type ON scheduled_notifications(notification_type)')

            # Update version
            cursor.execute('UPDATE db_version SET version = 8')
            current_version = 8

        # Version 9: Future migrations would go here
        # if current_version < 9:
        #    print("Running migration to version 9: ...")

        print(f"Database migrations complete. Current version: {current_version}")

    def update_username(self, user_id: str, username: str):
        with self.get_db_connection() as conn:
            c = conn.cursor()
            c.execute('''INSERT INTO users (user_id, username, galleons, sickles, knuts) 
                        VALUES (?, ?, 0, 0, 0)
                        ON CONFLICT(user_id) 
                        DO UPDATE SET username = ?''',
                     (user_id, username, username))
            conn.commit()

    @contextmanager
    def get_db_connection(self):
        """Context manager for database connections"""
        conn = None
        try:
            conn = sqlite3.connect('bank.db', timeout=60.0)
            conn.row_factory = sqlite3.Row
            conn.execute('PRAGMA journal_mode=WAL')
            conn.execute('PRAGMA busy_timeout=30000')
            conn.execute('PRAGMA synchronous=NORMAL')
            yield conn
            conn.commit()
        except Exception as e:
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            raise e
        finally:
            if conn:
                try:
                    conn.close()
                except:
                    pass

    def safe_execute(self, query, params=None):
        """Safe database execution with retries"""
        retries = 0
        while retries < self.max_retries:
            try:
                with self.get_db_connection() as conn:
                    c = conn.cursor()
                    if params:
                        c.execute(query, params)
                    else:
                        c.execute(query)
                    return c.fetchall()
            except sqlite3.OperationalError as e:
                retries += 1
                if retries == self.max_retries:
                    print(f"Failed after {retries} retries: {e}")
                    raise e
                time.sleep(self.retry_delay)

    def get_balance(self, user_id: str) -> int:
        """Get total balance in knuts"""
        with self.get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT galleons, sickles, knuts FROM users WHERE user_id = ?', (user_id,))
            result = c.fetchone()
            
            if result:
                galleons, sickles, knuts = result
                return (galleons * self.KNUTS_PER_GALLEON + 
                       sickles * self.KNUTS_PER_SICKLE + 
                       knuts)
            return 0

    def update_balance(self, user_id: str, knuts_amount: int, username: str = None):
        """Update user balance by converting to proper denominations"""
        with self.get_db_connection() as conn:
            c = conn.cursor()
            try:
                # Start transaction
                c.execute('BEGIN TRANSACTION')
                
                # Get current balance
                c.execute('SELECT galleons, sickles, knuts FROM users WHERE user_id = ?', (user_id,))
                result = c.fetchone()
                if result:
                    current_galleons, current_sickles, current_knuts = result
                else:
                    # Create new user with username if doesn't exist
                    c.execute('''INSERT INTO users (user_id, username, galleons, sickles, knuts)
                                VALUES (?, ?, 0, 0, 0)''', (user_id, username))
                    current_galleons = current_sickles = current_knuts = 0
                
                # Calculate total knuts
                total_knuts = (current_galleons * self.KNUTS_PER_GALLEON + 
                              current_sickles * self.KNUTS_PER_SICKLE + 
                              current_knuts + knuts_amount)
                
                if total_knuts < 0:
                    total_knuts = 0
                
                # Convert to denominations
                new_galleons = total_knuts // self.KNUTS_PER_GALLEON
                remaining = total_knuts % self.KNUTS_PER_GALLEON
                new_sickles = remaining // self.KNUTS_PER_SICKLE
                new_knuts = remaining % self.KNUTS_PER_SICKLE
                
                # Update database
                c.execute('''UPDATE users 
                            SET galleons = ?, sickles = ?, knuts = ?
                            WHERE user_id = ?''',
                         (new_galleons, new_sickles, new_knuts, user_id))
                
                # Commit transaction
                conn.commit()
                
                return new_galleons, new_sickles, new_knuts
                
            except Exception as e:
                conn.rollback()
                print(f"Error updating balance: {e}")
                raise

    def convert_to_all_denominations(self, knuts: int) -> tuple:
        """Convert knuts to galleons, sickles, and remaining knuts"""
        galleons = knuts // self.KNUTS_PER_GALLEON
        remaining_knuts = knuts % self.KNUTS_PER_GALLEON
        sickles = remaining_knuts // self.KNUTS_PER_SICKLE
        final_knuts = remaining_knuts % self.KNUTS_PER_SICKLE
        return (galleons, sickles, final_knuts)

    def format_currency(self, knuts: int) -> str:
        """Format currency amount into readable string"""
        if knuts <= 0:
            return f"{self.currency_emoji['knut']} **0** Knuts"
        
        galleons, sickles, knuts = self.convert_to_all_denominations(knuts)
        parts = []
        if galleons > 0:
            parts.append(f"{self.currency_emoji['galleon']} **{galleons}** Galleons")
        if sickles > 0:
            parts.append(f"{self.currency_emoji['sickle']} **{sickles}** Sickles")
        if knuts > 0 or not parts:  # Show knuts if it's the only currency or there are some
            parts.append(f"{self.currency_emoji['knut']} **{knuts}** Knuts")
        return ", ".join(parts)

    def format_currency_short(self, knuts: int) -> str:
        """Format currency amount into short readable string (e.g., 10G 5S 3K)"""
        if knuts <= 0:
            return "0K"
        
        galleons, sickles, knuts = self.convert_to_all_denominations(knuts)
        parts = []
        if galleons > 0:
            parts.append(f"{galleons}G")
        if sickles > 0:
            parts.append(f"{sickles}S")
        if knuts > 0 or not parts:  # Show knuts if it's the only currency or there are some
            parts.append(f"{knuts}K")
        return " ".join(parts)

    def normalize_currency(self, knuts: int) -> int:
        """Convert excess lower currencies to higher ones"""
        if knuts < 0:
            return 0  # Return 0 instead of negative values
            
        galleons = knuts // self.KNUTS_PER_GALLEON
        remaining_knuts = knuts % self.KNUTS_PER_GALLEON
        sickles = remaining_knuts // self.KNUTS_PER_SICKLE
        final_knuts = remaining_knuts % self.KNUTS_PER_SICKLE
        
        return (galleons * self.KNUTS_PER_GALLEON) + (sickles * self.KNUTS_PER_SICKLE) + final_knuts

    def parse_role_input(self, role_input: str, guild) -> str:
        """Parse role input and return role ID as string.
        Handles role mentions (<@&ID>), role names, and raw IDs."""
        if not role_input:
            return None

        # Remove whitespace
        role_input = role_input.strip()

        # Check if it's a role mention (<@&ID>)
        if role_input.startswith('<@&') and role_input.endswith('>'):
            return role_input[3:-1]  # Extract ID from <@&ID>

        # Check if it's already a role ID (all digits)
        if role_input.isdigit():
            return role_input

        # Try to find role by name
        if guild:
            for role in guild.roles:
                if role.name.lower() == role_input.lower():
                    return str(role.id)

        # If nothing matches, return the original input
        return role_input

    def get_cooldown(self, user_id: str, cooldown_type: str) -> str:
        try:
            with self.get_db_connection() as conn:
                c = conn.cursor()
                c.execute(f'SELECT {cooldown_type} FROM cooldowns WHERE user_id = ?', (user_id,))
                result = c.fetchone()
                return result[0] if result else None
        except Exception as e:
            print(f"Error getting cooldown: {e}")
            return None

    def set_cooldown(self, user_id: str, cooldown_type: str, time: str):
        try:
            with self.get_db_connection() as conn:
                c = conn.cursor()
                c.execute('''INSERT INTO cooldowns (user_id, ''' + cooldown_type + ''')
                           VALUES (?, ?)
                           ON CONFLICT(user_id) 
                           DO UPDATE SET ''' + cooldown_type + ''' = ?''',
                        (user_id, time, time))
                conn.commit()
        except Exception as e:
            print(f"Error setting cooldown: {e}")
            raise

    def log_transaction(self, user_id: str, amount: int, type: str, modifier_id: str, details: str):
        """Log a transaction with proper connection and transaction handling."""
        with self.get_db_connection() as conn:
            c = conn.cursor()
            try:
                c.execute('BEGIN TRANSACTION')
                c.execute('''INSERT INTO transactions 
                            (user_id, amount, type, timestamp, modifier_id, details)
                            VALUES (?, ?, ?, datetime('now'), ?, ?)''',
                         (user_id, amount, type, modifier_id, details))
                conn.commit()
            except Exception as e:
                conn.rollback()
                print(f"Error logging transaction: {e}")
                raise

    def update_profile(self, user_id: str, favorite_spells: str, pets: str, bio: str):
        """Update user profile information."""
        with self.get_db_connection() as conn:
            c = conn.cursor()
            c.execute('''UPDATE users 
                         SET favorite_spells = ?, pets = ?, bio = ?
                         WHERE user_id = ?''',
                       (favorite_spells, pets, bio, user_id))
            conn.commit()

    def get_profile(self, user_id: str) -> dict:
        """Retrieve user profile information."""
        with self.get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT username, favorite_spells, pets, bio FROM users WHERE user_id = ?', (user_id,))
            result = c.fetchone()
            if result:
                return {
                    "username": result[0],
                    "favorite_spells": result[1],
                    "pets": result[2],
                    "bio": result[3]
                }
            return None

    def is_vip(self, user_id: str) -> bool:
        """Check if a user has an active VIP membership."""
        with self.get_db_connection() as conn:
            c = conn.cursor()
            c.execute('''
                SELECT 1 FROM vip_memberships 
                WHERE user_id = ? AND expiry_date > datetime('now')
            ''', (user_id,))
            return bool(c.fetchone())
    
    def get_vip_info(self, user_id: str) -> dict:
        """Get detailed VIP membership information for a user."""
        with self.get_db_connection() as conn:
            c = conn.cursor()
            c.execute('''
                SELECT tier, start_date, expiry_date
                FROM vip_memberships 
                WHERE user_id = ? AND expiry_date > datetime('now')
            ''', (user_id,))
            result = c.fetchone()
            
            if not result:
                return None
            
            tier, start_date, expiry_date = result
            
            # Calculate remaining time
            expiry = datetime.fromisoformat(expiry_date.replace('Z', '+00:00'))
            now = datetime.now()
            remaining = expiry - now
            
            return {
                "tier": tier,
                "name": self.VIP_TIERS[tier]["name"],
                "start_date": start_date,
                "expiry_date": expiry_date,
                "purchase_date": start_date,  # Use start_date for purchase_date
                "days_remaining": remaining.days,
                "hours_remaining": remaining.seconds // 3600
            }
    
    def add_vip_membership(self, user_id: str, tier: str) -> bool:
        """Add or update VIP membership for a user."""
        if tier not in self.VIP_TIERS:
            return False
        
        try:
            with self.get_db_connection() as conn:
                c = conn.cursor()
                
                # Calculate expiry date
                now = datetime.now()
                duration_days = self.VIP_TIERS[tier]["duration"]
                expiry_date = now + timedelta(days=duration_days)
                
                # Insert or update membership
                c.execute('''
                    INSERT INTO vip_memberships 
                    (user_id, tier, start_date, expiry_date)
                    VALUES (?, ?, ?, ?)
                    ON CONFLICT(user_id) 
                    DO UPDATE SET 
                        tier = ?, 
                        start_date = ?, 
                        expiry_date = ?
                ''', (
                    user_id, 
                    tier, 
                    now.isoformat(), 
                    expiry_date.isoformat(),
                    tier, 
                    now.isoformat(), 
                    expiry_date.isoformat()
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"Error adding VIP membership: {e}")
            return False

    def extend_vip_membership(self, user_id: str, tier: str) -> bool:
        """Extend an existing VIP membership."""
        try:
            with self.get_db_connection() as conn:
                c = conn.cursor()
                
                # Check if user already has a membership
                c.execute('''
                    SELECT expiry_date 
                    FROM vip_memberships 
                    WHERE user_id = ?
                ''', (user_id,))
                
                result = c.fetchone()
                
                if result:
                    # Calculate new expiry date based on current expiry
                    current_expiry = datetime.fromisoformat(result[0].replace('Z', '+00:00'))
                    duration_days = self.VIP_TIERS[tier]["duration"]
                    
                    # If expired, start from now
                    if current_expiry < datetime.now():
                        new_expiry = datetime.now() + timedelta(days=duration_days)
                    else:
                        new_expiry = current_expiry + timedelta(days=duration_days)
                    
                    # Update membership
                    c.execute('''
                        UPDATE vip_memberships
                        SET tier = ?,
                            expiry_date = ?
                        WHERE user_id = ?
                    ''', (tier, new_expiry.isoformat(), user_id))
                    
                    conn.commit()
                    return True
                else:
                    # If no existing membership, create new one
                    return self.add_vip_membership(user_id, tier)
                
        except Exception as e:
            print(f"Error extending VIP membership: {e}")
            return False

    def update_vip_notification(self, user_id: str, notification_type: str) -> bool:
        """Update the last notification time for a VIP user."""
        valid_types = ["work", "income"]
        if notification_type not in valid_types:
            return False

        column_name = f"last_{notification_type}_notification"

        try:
            with self.get_db_connection() as conn:
                c = conn.cursor()
                c.execute(f'''
                    UPDATE vip_memberships
                    SET {column_name} = datetime('now')
                    WHERE user_id = ? AND expiry_date > datetime('now')
                ''', (user_id,))
                conn.commit()
                return c.rowcount > 0
        except Exception as e:
            print(f"Error updating VIP notification: {e}")
            return False

    def store_scheduled_notification(self, user_id: str, notification_type: str, scheduled_time: datetime) -> int:
        """Store a scheduled notification in the database. Returns notification ID or None if failed."""
        try:
            with self.get_db_connection() as conn:
                c = conn.cursor()

                # Ensure the table exists
                self._ensure_notifications_table(c)

                c.execute('''
                    INSERT INTO scheduled_notifications
                    (user_id, notification_type, scheduled_time, status)
                    VALUES (?, ?, ?, 'pending')
                ''', (user_id, notification_type, scheduled_time.isoformat()))
                conn.commit()
                return c.lastrowid
        except Exception as e:
            print(f"Error storing scheduled notification: {e}")
            return None

    def mark_notification_sent(self, notification_id: int) -> bool:
        """Mark a notification as sent in the database."""
        try:
            with self.get_db_connection() as conn:
                c = conn.cursor()

                # Ensure the table exists
                self._ensure_notifications_table(c)

                c.execute('''
                    UPDATE scheduled_notifications
                    SET status = 'sent', sent_at = datetime('now')
                    WHERE id = ?
                ''', (notification_id,))
                conn.commit()
                return c.rowcount > 0
        except Exception as e:
            print(f"Error marking notification as sent: {e}")
            return False

    def mark_notification_failed(self, notification_id: int) -> bool:
        """Mark a notification as failed in the database."""
        try:
            with self.get_db_connection() as conn:
                c = conn.cursor()

                # Ensure the table exists
                self._ensure_notifications_table(c)

                c.execute('''
                    UPDATE scheduled_notifications
                    SET status = 'failed'
                    WHERE id = ?
                ''', (notification_id,))
                conn.commit()
                return c.rowcount > 0
        except Exception as e:
            print(f"Error marking notification as failed: {e}")
            return False

    def get_pending_notifications(self) -> list:
        """Get all pending notifications from the database."""
        try:
            with self.get_db_connection() as conn:
                c = conn.cursor()

                # Ensure the table exists
                self._ensure_notifications_table(c)

                c.execute('''
                    SELECT id, user_id, notification_type, scheduled_time
                    FROM scheduled_notifications
                    WHERE status = 'pending'
                    ORDER BY scheduled_time ASC
                ''')
                return c.fetchall()
        except Exception as e:
            print(f"Error getting pending notifications: {e}")
            return []

    def _ensure_notifications_table(self, cursor):
        """Ensure the scheduled_notifications table exists."""
        try:
            cursor.execute('''CREATE TABLE IF NOT EXISTS scheduled_notifications
                            (id INTEGER PRIMARY KEY AUTOINCREMENT,
                             user_id TEXT NOT NULL,
                             notification_type TEXT NOT NULL,
                             scheduled_time TEXT NOT NULL,
                             created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                             sent_at TEXT,
                             status TEXT DEFAULT 'pending')''')

            # Add indexes for performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_user_id ON scheduled_notifications(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_scheduled_time ON scheduled_notifications(scheduled_time)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_status ON scheduled_notifications(status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_type ON scheduled_notifications(notification_type)')

        except Exception as e:
            print(f"Error ensuring notifications table: {e}")

    def get_pending_notification_ids(self, user_id: str, notification_type: str = None) -> list:
        """Get IDs of pending notifications for a user. Returns list of notification IDs."""
        try:
            with self.get_db_connection() as conn:
                c = conn.cursor()

                # Ensure the table exists
                self._ensure_notifications_table(c)

                if notification_type:
                    c.execute('''
                        SELECT id FROM scheduled_notifications
                        WHERE user_id = ? AND notification_type = ? AND status = 'pending'
                    ''', (user_id, notification_type))
                else:
                    c.execute('''
                        SELECT id FROM scheduled_notifications
                        WHERE user_id = ? AND status = 'pending'
                    ''', (user_id,))
                return [row[0] for row in c.fetchall()]
        except Exception as e:
            print(f"Error getting pending notification IDs: {e}")
            return []

    def cancel_pending_notifications(self, user_id: str, notification_type: str = None) -> int:
        """Cancel pending notifications for a user. Returns number of cancelled notifications."""
        try:
            with self.get_db_connection() as conn:
                c = conn.cursor()

                # Ensure the table exists
                self._ensure_notifications_table(c)

                if notification_type:
                    c.execute('''
                        DELETE FROM scheduled_notifications
                        WHERE user_id = ? AND notification_type = ? AND status = 'pending'
                    ''', (user_id, notification_type))
                else:
                    c.execute('''
                        DELETE FROM scheduled_notifications
                        WHERE user_id = ? AND status = 'pending'
                    ''', (user_id,))
                conn.commit()
                return c.rowcount
        except Exception as e:
            print(f"Error cancelling notifications: {e}")
            return 0

    def cleanup_old_notifications(self, days_old: int = 7) -> int:
        """Clean up old notification records. Returns number of deleted records."""
        try:
            with self.get_db_connection() as conn:
                c = conn.cursor()

                # Ensure the table exists
                self._ensure_notifications_table(c)

                c.execute('''
                    DELETE FROM scheduled_notifications
                    WHERE status IN ('sent', 'failed')
                    AND datetime(created_at) < datetime('now', '-{} days')
                '''.format(days_old))
                conn.commit()
                return c.rowcount
        except Exception as e:
            print(f"Error cleaning up old notifications: {e}")
            return 0

    def cleanup_notifications_for_missing_users(self, bot) -> int:
        """Clean up notifications for users who are no longer in any mutual servers."""
        try:
            with self.get_db_connection() as conn:
                c = conn.cursor()

                # Ensure the table exists
                self._ensure_notifications_table(c)

                # Get all users with pending notifications
                c.execute('''
                    SELECT DISTINCT user_id FROM scheduled_notifications
                    WHERE status = 'pending'
                ''')
                users_with_notifications = c.fetchall()

                cleaned_count = 0
                for (user_id,) in users_with_notifications:
                    # Check if user is in any mutual server
                    is_in_mutual_server = False
                    for guild in bot.guilds:
                        if guild.get_member(int(user_id)):
                            is_in_mutual_server = True
                            break

                    if not is_in_mutual_server:
                        # User is not in any mutual server, clean up their notifications
                        c.execute('''
                            DELETE FROM scheduled_notifications
                            WHERE user_id = ? AND status = 'pending'
                        ''', (user_id,))
                        cleaned_count += c.rowcount
                        print(f"Cleaned up notifications for user {user_id} (not in any mutual servers)")

                conn.commit()
                return cleaned_count
        except Exception as e:
            print(f"Error cleaning up notifications for missing users: {e}")
            return 0

    def log_to_channel(self, bot, embed):
        """Send log message to the configured log channel"""
        try:
            log_channel_id = self.config.get('log_channel_id')
            if not log_channel_id:
                print("No log channel configured. Skipping log message.")
                return
            
            log_channel = bot.get_channel(log_channel_id)
            if log_channel:
                return bot.loop.create_task(log_channel.send(embed=embed))
            else:
                print(f"Could not find log channel with ID {log_channel_id}")
        except Exception as e:
            print(f"Error sending log message: {e}")

    # Add this helper method to check if a user can afford an item
    def can_afford_item(self, user_id: str, price: int) -> bool:
        with self.get_db_connection() as conn:
            c = conn.cursor()
            c.execute('''SELECT (galleons * ? + sickles * ? + knuts) as total_knuts 
                         FROM users WHERE user_id = ?''',
                      (self.KNUTS_PER_GALLEON, self.KNUTS_PER_SICKLE, user_id))
            result = c.fetchone()
            if not result:
                return False
            return result[0] >= price

    # Add this helper method to get item details
    def get_item_details(self, item_id: int) -> dict:
        with self.get_db_connection() as conn:
            c = conn.cursor()
            c.execute('''SELECT id, name, price, category, description, properties, required_role 
                         FROM shop_items WHERE id = ?''', (item_id,))
            result = c.fetchone()
            if not result:
                return None
            return dict(result)

    # Add this helper method to add item to inventory
    def add_to_inventory(self, user_id: str, item_id: int, category: str):
        with self.get_db_connection() as conn:
            c = conn.cursor()
            try:
                c.execute('BEGIN TRANSACTION')
                c.execute('''INSERT INTO inventory 
                            (user_id, item_id, category) 
                            VALUES (?, ?, ?)''',
                         (user_id, item_id, category))
                conn.commit()
                return True
            except Exception as e:
                conn.rollback()
                print(f"Error adding item to inventory: {e}")
                return False

    def process_purchase(self, user_id: str, item_id: int, category: str, price: int, name: str, description: str = None) -> bool:
        """Process a purchase atomically. Returns True if successful, False otherwise."""
        with self.get_db_connection() as conn:
            c = conn.cursor()
            try:
                # Check balance
                c.execute('''SELECT (galleons * ? + sickles * ? + knuts) as total_knuts 
                            FROM users WHERE user_id = ?''',
                         (self.KNUTS_PER_GALLEON, self.KNUTS_PER_SICKLE, user_id))
                result = c.fetchone()
                current_balance = result[0] if result else 0

                if current_balance < price:
                    return False
                
                # If it's a Wand or Broom, check if user already has one
                if category in ["Wands", "Brooms"]:
                    c.execute('''
                        SELECT COUNT(*) FROM inventory i
                        JOIN shop_items s ON i.item_id = s.id
                        WHERE i.user_id = ? 
                        AND s.category = ?
                        AND i.is_removed_item = 0
                    ''', (user_id, category))
                    
                    if c.fetchone()[0] > 0:
                        # User already has an item of this category
                        return False

                # Calculate new balance
                new_total = current_balance - price
                new_galleons = new_total // self.KNUTS_PER_GALLEON
                remaining = new_total % self.KNUTS_PER_GALLEON
                new_sickles = remaining // self.KNUTS_PER_SICKLE
                new_knuts = remaining % self.KNUTS_PER_SICKLE

                # Update balance
                c.execute('''UPDATE users 
                           SET galleons = ?, sickles = ?, knuts = ?
                           WHERE user_id = ?''',
                        (new_galleons, new_sickles, new_knuts, user_id))

                # Add to inventory
                c.execute('''INSERT INTO inventory 
                           (user_id, item_id, category) 
                           VALUES (?, ?, ?)''',
                        (user_id, item_id, category))

                # Log transaction
                c.execute('''INSERT INTO transactions 
                           (user_id, amount, type, timestamp, modifier_id, details)
                           VALUES (?, ?, ?, datetime('now'), ?, ?)''',
                        (user_id, -price, 'purchase', user_id, f"Purchased {name}"))

                return True

            except Exception as e:
                print(f"Error processing purchase: {e}")
                return False

bank = BankSystem()

# VIP notification tracking
class VIPNotificationManager:
    def __init__(self, bot):
        self.bot = bot
        self.active_tasks = {}  # notification_id -> asyncio.Task (for cancellation)
        print("VIP Notification Manager initialized")
    
    async def schedule_notification(self, user_id: str, cooldown_type: str, expiry_time: datetime):
        """Schedule a notification for when a cooldown expires"""
        try:
            # Calculate seconds until notification should be sent
            now = datetime.now()
            if expiry_time <= now:
                # Already expired, don't schedule
                return False

            # First get existing notification IDs before cancelling them
            existing_notification_ids = bank.get_pending_notification_ids(user_id, cooldown_type)

            # Cancel existing asyncio tasks for these notifications
            for existing_id in existing_notification_ids:
                if existing_id in self.active_tasks:
                    old_task = self.active_tasks[existing_id]
                    if not old_task.done():
                        old_task.cancel()
                        print(f"Cancelled existing notification task {existing_id} for user {user_id}")
                    # Remove from active tasks
                    self.active_tasks.pop(existing_id, None)

            # Now cancel the database records
            cancelled_count = bank.cancel_pending_notifications(user_id, cooldown_type)
            if cancelled_count > 0:
                print(f"Cancelled {cancelled_count} pending {cooldown_type} notifications for user {user_id}")

            # Store the new notification in the database
            notification_id = bank.store_scheduled_notification(user_id, cooldown_type, expiry_time)
            if not notification_id:
                print(f"Failed to store notification in database for user {user_id}")
                return False

            # Calculate delay in seconds
            delay_seconds = (expiry_time - now).total_seconds()

            # Schedule new task with asyncio
            self.active_tasks[notification_id] = asyncio.create_task(
                self._send_notification_after_delay(notification_id, user_id, cooldown_type, delay_seconds)
            )

            # Return success
            return True

        except Exception as e:
            print(f"Error scheduling notification for {user_id}: {e}")
            return False
    
    async def schedule_expiry_notification(self, user_id: str, notification_time: datetime):
        """Schedule a notification before VIP membership expires"""
        try:
            # Calculate seconds until notification should be sent
            now = datetime.now()
            if notification_time <= now:
                # Already expired, don't schedule
                return False

            # First get existing notification IDs before cancelling them
            existing_notification_ids = bank.get_pending_notification_ids(user_id, 'expiry')

            # Cancel existing asyncio tasks for these notifications
            for existing_id in existing_notification_ids:
                if existing_id in self.active_tasks:
                    old_task = self.active_tasks[existing_id]
                    if not old_task.done():
                        old_task.cancel()
                        print(f"Cancelled existing expiry notification task {existing_id} for user {user_id}")
                    # Remove from active tasks
                    self.active_tasks.pop(existing_id, None)

            # Now cancel the database records
            cancelled_count = bank.cancel_pending_notifications(user_id, 'expiry')
            if cancelled_count > 0:
                print(f"Cancelled {cancelled_count} pending expiry notifications for user {user_id}")

            # Store the new notification in the database
            notification_id = bank.store_scheduled_notification(user_id, 'expiry', notification_time)
            if not notification_id:
                print(f"Failed to store expiry notification in database for user {user_id}")
                return False

            # Calculate delay in seconds
            delay_seconds = (notification_time - now).total_seconds()

            # Schedule new task with asyncio
            self.active_tasks[notification_id] = asyncio.create_task(
                self._send_expiry_notification_after_delay(notification_id, user_id, delay_seconds)
            )

            # Return success
            return True

        except Exception as e:
            print(f"Error scheduling expiry notification for {user_id}: {e}")
            return False
    
    async def _send_notification_after_delay(self, notification_id: int, user_id: str, cooldown_type: str, delay_seconds: float):
        """Internal method to wait and then send notification"""
        try:
            # Wait until cooldown expires
            await asyncio.sleep(delay_seconds)

            # Verify user is still VIP before sending
            if not bank.is_vip(user_id):
                print(f"User {user_id} is no longer VIP, skipping notification")
                bank.mark_notification_failed(notification_id)
                return

            # Check if user is still in any mutual servers before attempting to send DM
            user = None
            is_in_mutual_server = False

            # Check all guilds the bot is in to see if user is still a member
            for guild in self.bot.guilds:
                member = guild.get_member(int(user_id))
                if member:
                    user = member
                    is_in_mutual_server = True
                    break

            if not is_in_mutual_server:
                print(f"User {user_id} is not in any mutual servers, skipping notification")
                bank.mark_notification_failed(notification_id)
                return

            # If we found the user in a server, try to get the User object for DM
            if not user:
                try:
                    user = await self.bot.fetch_user(int(user_id))
                    if not user:
                        print(f"Could not fetch user {user_id}")
                        bank.mark_notification_failed(notification_id)
                        return
                except discord.NotFound:
                    print(f"User {user_id} not found")
                    bank.mark_notification_failed(notification_id)
                    return
                except discord.HTTPException as e:
                    print(f"HTTP error fetching user {user_id}: {e}")
                    bank.mark_notification_failed(notification_id)
                    return

            # Create appropriate embed
            if cooldown_type == "work":
                embed = Embed(
                    title=f"{bank.work_emoji} Work Available!",
                    description="Your work cooldown has expired! You can now use `/work` to earn more money.",
                    color=bank.vip_color
                )
                embed.set_footer(text="This is a VIP membership benefit")

            elif cooldown_type == "income":
                embed = Embed(
                    title=f"{bank.bank_emoji} Income Available!",
                    description="Your income cooldown has expired! You can now use `/collect_income` to collect your weekly income.",
                    color=bank.vip_color
                )
                embed.set_footer(text="This is a VIP membership benefit")
            else:
                print(f"Unknown notification type: {cooldown_type}")
                bank.mark_notification_failed(notification_id)
                return

            # Send the notification with rate limiting
            try:
                await rate_limiter.execute_with_rate_limit("user.send", user.send, embed=embed)

                # Mark as sent in database
                bank.mark_notification_sent(notification_id)

                # Update notification timestamp in VIP table
                bank.update_vip_notification(user_id, cooldown_type)

                print(f"Successfully sent {cooldown_type} notification to VIP user {user_id}")

            except discord.Forbidden:
                print(f"Cannot send DM to user {user_id} (DMs disabled or blocked)")
                bank.mark_notification_failed(notification_id)
            except discord.HTTPException as e:
                print(f"Failed to send DM to user {user_id}: {e}")
                bank.mark_notification_failed(notification_id)

        except asyncio.CancelledError:
            # Task was cancelled, just exit
            print(f"Notification task for user {user_id} was cancelled")
            return
        except Exception as e:
            print(f"Unexpected error sending notification to {user_id}: {e}")
            bank.mark_notification_failed(notification_id)
        finally:
            # Remove task from tracking regardless of success/failure
            if notification_id in self.active_tasks:
                self.active_tasks.pop(notification_id, None)
    
    async def _send_expiry_notification_after_delay(self, notification_id: int, user_id: str, delay_seconds: float):
        """Internal method to wait and then send VIP expiry notification"""
        try:
            # Wait until notification time
            await asyncio.sleep(delay_seconds)

            # Verify user is still VIP before sending
            vip_info = bank.get_vip_info(user_id)
            if not vip_info:
                print(f"User {user_id} is no longer VIP, skipping expiry notification")
                bank.mark_notification_failed(notification_id)
                return

            # Check if user is still in any mutual servers before attempting to send DM
            user = None
            is_in_mutual_server = False

            # Check all guilds the bot is in to see if user is still a member
            for guild in self.bot.guilds:
                member = guild.get_member(int(user_id))
                if member:
                    user = member
                    is_in_mutual_server = True
                    break

            if not is_in_mutual_server:
                print(f"User {user_id} is not in any mutual servers, skipping expiry notification")
                bank.mark_notification_failed(notification_id)
                return

            # If we found the user in a server, try to get the User object for DM
            if not user:
                try:
                    user = await self.bot.fetch_user(int(user_id))
                    if not user:
                        print(f"Could not fetch user {user_id}")
                        bank.mark_notification_failed(notification_id)
                        return
                except discord.NotFound:
                    print(f"User {user_id} not found")
                    bank.mark_notification_failed(notification_id)
                    return
                except discord.HTTPException as e:
                    print(f"HTTP error fetching user {user_id}: {e}")
                    bank.mark_notification_failed(notification_id)
                    return

            # Create embed with renewal information
            expiry_embed = Embed(
                title=f"{bank.vip_emoji} VIP Membership Expiring Soon!",
                description="Your VIP membership will expire in about 24 hours. Renew now to keep enjoying VIP benefits!",
                color=bank.vip_color
            )

            # Add membership details
            expiry_embed.add_field(
                name="Membership Details",
                value=f"**Current Tier:** {vip_info['name']}\n"
                      f"**Expires:** <t:{int(datetime.fromisoformat(vip_info['expiry_date'].replace('Z', '+00:00')).timestamp())}:f>\n"
                      f"**Time Remaining:** {vip_info['days_remaining']} days and {vip_info['hours_remaining']} hours",
                inline=False
            )

            # Add renewal instructions
            expiry_embed.add_field(
                name="How to Renew",
                value="Use `/buy_vip` command to extend your membership.",
                inline=False
            )

            # Set footer
            expiry_embed.set_footer(text="This is a VIP membership notification")

            # Send DM with rate limiting
            try:
                await rate_limiter.execute_with_rate_limit("user.send", user.send, embed=expiry_embed)

                # Mark as sent in database
                bank.mark_notification_sent(notification_id)

                print(f"Successfully sent VIP expiry notification to user {user_id}")

            except discord.Forbidden:
                print(f"Cannot send DM to user {user_id} (DMs disabled or blocked)")
                bank.mark_notification_failed(notification_id)
            except discord.HTTPException as e:
                print(f"Failed to send DM to user {user_id}: {e}")
                bank.mark_notification_failed(notification_id)

        except asyncio.CancelledError:
            # Task was cancelled, just exit
            print(f"Expiry notification task for user {user_id} was cancelled")
            return
        except Exception as e:
            print(f"Unexpected error sending expiry notification to {user_id}: {e}")
            bank.mark_notification_failed(notification_id)
        finally:
            # Remove task from tracking
            if notification_id in self.active_tasks:
                self.active_tasks.pop(notification_id, None)
    
    async def schedule_all_pending_notifications(self):
        """Schedule notifications for all VIP users with active cooldowns and handle missed notifications"""
        try:
            print("Processing VIP notifications on startup...")

            # Track notification counts
            missed_notifications = 0
            scheduled_notifications = 0

            now = datetime.now()

            # First, handle any missed notifications from database
            pending_notifications = bank.get_pending_notifications()
            print(f"Found {len(pending_notifications)} pending notifications in database")

            for notification_id, user_id, notification_type, scheduled_time_str in pending_notifications:
                try:
                    scheduled_time = datetime.fromisoformat(scheduled_time_str)

                    # Check if notification should have been sent already (missed)
                    if scheduled_time <= now:
                        print(f"Found missed {notification_type} notification for user {user_id}, sending immediately")

                        # Send immediately with minimal delay
                        self.active_tasks[notification_id] = asyncio.create_task(
                            self._send_notification_after_delay(notification_id, user_id, notification_type, 1.0)
                        )
                        missed_notifications += 1
                    else:
                        # Schedule for future
                        delay_seconds = (scheduled_time - now).total_seconds()

                        if notification_type == 'expiry':
                            self.active_tasks[notification_id] = asyncio.create_task(
                                self._send_expiry_notification_after_delay(notification_id, user_id, delay_seconds)
                            )
                        else:
                            self.active_tasks[notification_id] = asyncio.create_task(
                                self._send_notification_after_delay(notification_id, user_id, notification_type, delay_seconds)
                            )
                        scheduled_notifications += 1

                except Exception as e:
                    print(f"Error processing pending notification {notification_id}: {e}")
                    bank.mark_notification_failed(notification_id)

            # Now schedule new notifications for VIP users with active cooldowns (but no pending notifications)
            with bank.get_db_connection() as conn:
                c = conn.cursor()

                # Get VIP users with cooldowns who don't already have pending notifications
                c.execute('''
                    SELECT DISTINCT v.user_id, v.expiry_date, c.work_cooldown, c.income_cooldown
                    FROM vip_memberships v
                    LEFT JOIN cooldowns c ON v.user_id = c.user_id
                    WHERE v.expiry_date > datetime('now')
                    AND v.user_id NOT IN (
                        SELECT DISTINCT user_id FROM scheduled_notifications
                        WHERE status = 'pending'
                    )
                ''')

                vip_users = c.fetchall()
                print(f"Found {len(vip_users)} VIP users without pending notifications to check")

                for user_id, expiry_date, work_cooldown, income_cooldown in vip_users:
                    # Schedule work notification if cooldown exists
                    if work_cooldown:
                        work_expiry = datetime.fromisoformat(work_cooldown) + timedelta(hours=4)
                        if work_expiry > now:  # Only schedule if in future
                            if await self.schedule_notification(user_id, "work", work_expiry):
                                scheduled_notifications += 1

                    # Schedule income notification if cooldown exists
                    if income_cooldown:
                        income_expiry = datetime.fromisoformat(income_cooldown) + timedelta(weeks=1)
                        if income_expiry > now:  # Only schedule if in future
                            if await self.schedule_notification(user_id, "income", income_expiry):
                                scheduled_notifications += 1

                    # Schedule expiry notification if membership expires within 24 hours
                    try:
                        expiry_time = datetime.fromisoformat(expiry_date.replace('Z', '+00:00'))

                        # If expiry is between 0-24 hours away, schedule notification
                        time_until_expiry = expiry_time - now
                        if 0 < time_until_expiry.total_seconds() < 86400:  # Less than 24 hours
                            # Send notification immediately if less than 23 hours remain
                            if time_until_expiry.total_seconds() < 82800:  # 23 hours
                                notification_id = bank.store_scheduled_notification(user_id, 'expiry', now)
                                if notification_id:
                                    self.active_tasks[notification_id] = asyncio.create_task(
                                        self._send_expiry_notification_after_delay(notification_id, user_id, 1.0)
                                    )
                                    scheduled_notifications += 1
                            else:
                                # Otherwise schedule for the 24-hour mark
                                notification_time = expiry_time - timedelta(hours=24)
                                if await self.schedule_expiry_notification(user_id, notification_time):
                                    scheduled_notifications += 1
                    except Exception as exp_err:
                        print(f"Error scheduling expiry notification for {user_id}: {exp_err}")

                # Print summary report
                print(f"VIP Notification Startup Summary:")
                print(f"- Missed notifications sent immediately: {missed_notifications}")
                print(f"- Future notifications scheduled: {scheduled_notifications}")
                print(f"- Total active notification tasks: {len(self.active_tasks)}")

        except Exception as e:
            print(f"Error processing startup notifications: {e}")

# Initialize notification manager
vip_notifications = None

# Persistent view for admin VIP management
class AdminVIPView(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)  # Persistent view that doesn't timeout
    
    @discord.ui.button(label="Grant VIP", style=discord.ButtonStyle.green, custom_id="admin_vip:grant")
    async def grant_vip(self, interaction: discord.Interaction, button: discord.ui.Button):
        # This is a persistent button that will open the admin_vip command with grant action
        await interaction.response.send_modal(
            AdminVIPModal(action="grant", title="Grant VIP Membership")
        )
    
    @discord.ui.button(label="Revoke VIP", style=discord.ButtonStyle.red, custom_id="admin_vip:revoke")
    async def revoke_vip(self, interaction: discord.Interaction, button: discord.ui.Button):
        # This is a persistent button that will open the admin_vip command with revoke action
        await interaction.response.send_modal(
            AdminVIPModal(action="revoke", title="Revoke VIP Membership")
        )

# Modal for admin VIP management
class AdminVIPModal(discord.ui.Modal):
    def __init__(self, action: str, title: str):
        super().__init__(title=title)
        self.action = action
        
        # Add user ID input field
        self.user_id = discord.ui.TextInput(
            label="User ID",
            placeholder="Enter the user ID",
            required=True
        )
        self.add_item(self.user_id)
        
        # Add tier selection for grant action
        if action == "grant":
            self.tier = discord.ui.TextInput(
                label="VIP Tier",
                placeholder="weekly, monthly, or quarterly",
                required=True
            )
            self.add_item(self.tier)
    
    async def on_submit(self, interaction: discord.Interaction):
        # Check if user has banker role
        has_permission = False
        for role in interaction.user.roles:
            if role.id in bank.banker_roles:
                has_permission = True
                break
        
        if not has_permission and interaction.user.id != bot.owner_id:
            embed = Embed(
                title="❌ Access Denied",
                description="You don't have permission to manage VIP memberships!",
                color=bank.error_color
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return
        
        user_id = self.user_id.value.strip()
        
        # Process based on action
        if self.action == "grant":
            tier = self.tier.value.strip().lower()
            if tier not in ["weekly", "monthly", "quarterly"]:
                await interaction.response.send_message(
                    "Invalid tier! Please use 'weekly', 'monthly', or 'quarterly'.",
                    ephemeral=True
                )
                return
                
            # Grant VIP membership
            success = bank.add_vip_membership(user_id, tier)
            
            if success:
                # Get VIP info
                vip_info = bank.get_vip_info(user_id)
                expiry_timestamp = int(datetime.fromisoformat(vip_info['expiry_date'].replace('Z', '+00:00')).timestamp())
                
                # Send success message
                embed = Embed(
                    title=f"{bank.vip_emoji} VIP Membership Granted",
                    description=f"Successfully granted {vip_info['name']} to user ID: {user_id}",
                    color=bank.success_color
                )
                
                embed.add_field(
                    name="Membership Details",
                    value=f"**Tier:** {vip_info['name']}\n"
                          f"**Expires:** <t:{expiry_timestamp}:f>",
                    inline=False
                )
                
                # Log to channel
                log_embed = Embed(
                    title=f"{bank.vip_emoji} VIP Membership Granted",
                    description=f"{interaction.user.mention} granted VIP membership to user ID: {user_id}",
                    color=bank.vip_color,
                    timestamp=datetime.now()
                )
                log_embed.add_field(
                    name="Details",
                    value=f"**Tier:** {vip_info['name']}\n"
                          f"**Expires:** <t:{expiry_timestamp}:f>",
                    inline=False
                )
                log_embed.set_footer(text=f"Granted by: {interaction.user.id}")
                
                await bank.log_to_channel(bot, log_embed)
                await interaction.response.send_message(embed=embed, ephemeral=False)
            else:
                embed = Embed(
                    title="❌ Error",
                    description=f"Failed to grant VIP membership to user ID: {user_id}",
                    color=bank.error_color
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
        
        else:  # revoke
            # Delete from VIP memberships
            with bank.get_db_connection() as conn:
                c = conn.cursor()
                # Check if user has VIP first
                c.execute('SELECT 1 FROM vip_memberships WHERE user_id = ?', (user_id,))
                has_vip = bool(c.fetchone())
                
                if has_vip:
                    c.execute('DELETE FROM vip_memberships WHERE user_id = ?', (user_id,))
                    conn.commit()
                    
                    # Create success message
                    embed = Embed(
                        title="VIP Membership Revoked",
                        description=f"Successfully revoked VIP membership from user ID: {user_id}",
                        color=bank.success_color
                    )
                    
                    # Log to channel
                    log_embed = Embed(
                        title="VIP Membership Revoked",
                        description=f"{interaction.user.mention} revoked VIP membership from user ID: {user_id}",
                        color=bank.info_color,
                        timestamp=datetime.now()
                    )
                    log_embed.set_footer(text=f"Revoked by: {interaction.user.id}")
                    
                    await bank.log_to_channel(bot, log_embed)
                    await interaction.response.send_message(embed=embed, ephemeral=False)
                else:
                    embed = Embed(
                        title="❌ No Action Taken",
                        description=f"User ID {user_id} does not have an active VIP membership",
                        color=bank.error_color
                    )
                    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.event
async def on_ready():
    print(f'Logged in as {bot.user.name}')
    
    # Set owner_id from config
    bot.owner_id = bank.config.get('owner_id')
    print(f"Owner ID set from config: {bot.owner_id}")
    
    bot.add_view(AdminVIPView())  # Register persistent view
    
    # Sync commands with Discord
    try:
        print("Syncing commands with Discord...")
        await bot.tree.sync()
        print("Command sync complete!")
    except Exception as e:
        print(f"Error syncing commands: {e}")
    
    # Schedule database backups
    if not backup_database_midnight.is_running():
        backup_database_midnight.start()
        print("Midnight database backup scheduled")
    
    if not backup_database_noon.is_running():
        backup_database_noon.start()
        print("Noon database backup scheduled")
    
    # Start cooldown cleanup task
    if not cleanup_cooldowns.is_running():
        cleanup_cooldowns.start()
        print("Cooldown cleanup task started")

    # Start notification cleanup task
    if not cleanup_old_notifications.is_running():
        cleanup_old_notifications.start()
        print("Notification cleanup task started")

    # Initialize VIP notification manager and schedule pending notifications
    global vip_notifications
    vip_notifications = VIPNotificationManager(bot)
    await vip_notifications.schedule_all_pending_notifications()
    print("VIP notification system activated")

@tasks.loop(minutes=5)
async def cleanup_cooldowns():
    """Task to clean up expired cooldowns every 5 minutes"""
    try:
        cooldown_manager.cleanup_expired()
    except Exception as e:
        logger.error(f"Error in cooldown cleanup task: {e}")

@tasks.loop(hours=6)
async def cleanup_old_notifications():
    """Task to clean up old notification records every 6 hours"""
    try:
        # Clean up old sent/failed notifications
        deleted_count = bank.cleanup_old_notifications(days_old=7)
        if deleted_count > 0:
            logger.info(f"Cleaned up {deleted_count} old notification records")

        # Clean up notifications for users who left all servers
        missing_users_count = bank.cleanup_notifications_for_missing_users(bot)
        if missing_users_count > 0:
            logger.info(f"Cleaned up {missing_users_count} notifications for users who left all servers")

    except Exception as e:
        logger.error(f"Error in notification cleanup task: {e}")

# Custom cooldown check decorator for slash commands
def custom_cooldown(rate, per, bucket_type="user"):
    """
    Custom cooldown decorator for slash commands using our cooldown manager
    
    Parameters:
    - rate: Number of uses allowed
    - per: Time in seconds
    - bucket_type: 'user', 'guild', 'channel', or 'global'
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(interaction: discord.Interaction, *args, **kwargs):
            command_name = func.__name__
            
            # Skip cooldown check for work and collect_income commands
            # as they handle their own cooldowns via database
            if command_name in ["work", "collect_income"]:
                await func(interaction, *args, **kwargs)
                return
            
            # Check if command is on cooldown
            on_cooldown, remaining = cooldown_manager.is_on_cooldown(command_name, interaction, bucket_type)
            
            if on_cooldown:
                # Command is on cooldown
                embed = Embed(
                    title=f"{bank.time_emoji} Command Cooldown",
                    description=f"You need to wait **{cooldown_manager.format_remaining_time(remaining)}** before using this command again!",
                    color=bank.error_color
                )
                
                # Show VIP promotion if not a VIP and cooldown is substantial
                if remaining > 600 and not bank.is_vip(str(interaction.user.id)):
                    embed.add_field(
                        name=f"{bank.vip_emoji} VIP Benefit",
                        value="VIP members receive notifications when cooldowns expire! Use `/buy_vip` to purchase.",
                        inline=False
                    )
                
                await send_message(interaction, embed=embed, ephemeral=True)
                return
            
            # Execute the command
            try:
                await func(interaction, *args, **kwargs)
                # Set cooldown after successful execution
                cooldown_manager.set_cooldown(command_name, interaction, per, bucket_type)
            except Exception as e:
                # Don't set cooldown if command failed with an error
                logger.error(f"Error executing command {command_name}: {e}")
                raise
                
        return wrapper
    return decorator

@bot.event
async def on_command_error(ctx, error):
    print(f"Command error: {error}")

@bot.tree.error
async def on_app_command_error(interaction: discord.Interaction, error: app_commands.AppCommandError):
    try:
        if isinstance(error, app_commands.CommandOnCooldown):
            # Convert seconds to hours, minutes and seconds
            total_seconds = int(error.retry_after)
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            seconds = total_seconds % 60
            
            # Create a user-friendly time message
            time_message = ""
            if hours > 0:
                time_message += f"**{hours}** hours, "
            if minutes > 0 or hours > 0:
                time_message += f"**{minutes}** minutes and "
            time_message += f"**{seconds}** seconds"
            
            # Log the cooldown event
            logger.info(f"Cooldown: User {interaction.user.id} ({interaction.user.name}) hit cooldown on {interaction.command.name}, retry after {total_seconds}s")
            
            embed = Embed(
                title=f"{bank.time_emoji} Command Cooldown",
                description=f"You need to wait {time_message} before using this command again!",
                color=bank.error_color
            )
            
            # Show VIP promotion if command has a long cooldown (over 10 minutes)
            if total_seconds > 600 and not bank.is_vip(str(interaction.user.id)):
                embed.add_field(
                    name=f"{bank.vip_emoji} VIP Benefit",
                    value="VIP members receive notifications when cooldowns expire! Use `/buy_vip` to purchase.",
                    inline=False
                )
            
            # Send response based on interaction state
            if not interaction.response.is_done():
                await interaction.response.send_message(embed=embed, ephemeral=True)
            else:
                await interaction.followup.send(embed=embed, ephemeral=True)
        
        elif isinstance(error, app_commands.MissingPermissions):
            logger.warning(f"Permission error: User {interaction.user.id} ({interaction.user.name}) missing permissions for {interaction.command.name}")
            
            embed = Embed(
                title="❌ Missing Permissions",
                description="You don't have the required permissions to use this command.",
                color=bank.error_color
            )
            
            if not interaction.response.is_done():
                await interaction.response.send_message(embed=embed, ephemeral=True)
            else:
                await interaction.followup.send(embed=embed, ephemeral=True)
        
        elif isinstance(error, app_commands.BotMissingPermissions):
            logger.error(f"Bot permission error: Missing permissions for {interaction.command.name} in guild {interaction.guild_id}")
            
            embed = Embed(
                title="❌ Bot Missing Permissions",
                description="I don't have the required permissions to execute this command.",
                color=bank.error_color
            )
            
            if not interaction.response.is_done():
                await interaction.response.send_message(embed=embed, ephemeral=True)
            else:
                await interaction.followup.send(embed=embed, ephemeral=True)
        
        elif isinstance(error, discord.errors.DiscordServerError):
            # Handle Discord server errors (5xx)
            logger.error(f"Discord server error during {interaction.command.name}: {error}")
            
            embed = Embed(
                title="⚠️ Discord Server Error",
                description="Discord is experiencing issues. Please try again later.",
                color=bank.error_color
            )
            
            try:
                if not interaction.response.is_done():
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                else:
                    await interaction.followup.send(embed=embed, ephemeral=True)
            except Exception as e:
                logger.error(f"Failed to send Discord server error message: {e}")
        
        elif isinstance(error.__cause__, discord.errors.HTTPException) and error.__cause__.status == 429:
            # Specific handling for rate limit errors
            retry_after = getattr(error.__cause__, 'retry_after', 5)
            is_global = getattr(error.__cause__, 'global', False)
            
            logger.warning(f"Rate limit hit during {interaction.command.name}: retry after {retry_after}s, global: {is_global}")
            
            embed = Embed(
                title="⏱️ Rate Limit Reached",
                description=f"The bot is being used by too many people right now. Please try again in **{retry_after:.1f}** seconds.",
                color=bank.error_color
            )
            
            try:
                if not interaction.response.is_done():
                    await interaction.response.send_message(embed=embed, ephemeral=True)
                else:
                    await interaction.followup.send(embed=embed, ephemeral=True)
            except Exception as e:
                logger.error(f"Failed to send rate limit error message: {e}")
        
        else:
            # Log other errors with detailed information
            command_name = getattr(interaction.command, 'name', 'unknown')
            user_info = f"{interaction.user.id} ({interaction.user.name})"
            guild_info = f"{interaction.guild_id}" if interaction.guild else "DM"
            
            logger.error(f"Command error: {command_name} by {user_info} in {guild_info}: {error}")
            
            error_embed = Embed(
                title="❌ Command Error",
                description=f"An error occurred. Our team has been notified.",
                color=bank.error_color
            )
            
            if not interaction.response.is_done():
                await interaction.response.send_message(embed=error_embed, ephemeral=True)
            else:
                await interaction.followup.send(embed=error_embed, ephemeral=True)
    
    except Exception as e:
        logger.critical(f"Error in error handler: {e} (Original error: {error})")
        # Try one last fallback if everything else fails
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(
                    "An error occurred while processing your command.", 
                    ephemeral=True
                )
        except:
            pass  # At this point, we can't do anything else

@bot.tree.command(name="work", description="Work to earn money")
@app_commands.guild_only()
@custom_cooldown(1, 14400, "user")  # 1 use per 4 hours (14400 seconds)
async def work(interaction: discord.Interaction):
    try:
        # Defer response to prevent timeout
        await defer_response(interaction)
        
        user_id = str(interaction.user.id)
        username = interaction.user.display_name
        current_time = datetime.now()
        
        # Check cooldown - 4 hours
        last_work = bank.get_cooldown(user_id, 'work_cooldown')
        if last_work:
            last_work_time = datetime.fromisoformat(last_work)
            if current_time < last_work_time + timedelta(hours=4):
                time_remaining = (last_work_time + timedelta(hours=4) - current_time)
                hours_remaining = time_remaining.seconds // 3600
                minutes_remaining = (time_remaining.seconds % 3600) // 60
                seconds_remaining = time_remaining.seconds % 60
                
                embed = Embed(
                    title=f"{bank.time_emoji} Work Cooldown",
                    description=f"You need to rest for **{hours_remaining}** hours, **{minutes_remaining}** minutes and **{seconds_remaining}** seconds before working again!",
                    color=bank.error_color
                )
                
                # Show VIP promotion if not a VIP
                if not bank.is_vip(user_id):
                    embed.add_field(
                        name=f"{bank.vip_emoji} VIP Benefit",
                        value="VIP members receive notifications when cooldowns expire! Use `/buy_vip` to purchase.",
                        inline=False
                    )
                
                await send_message(interaction, embed=embed)
                return
        
        # Determine highest work role
        work_config = bank.default_work
        
        # Check if interaction.user is a Member (has roles) - fix for User object
        if hasattr(interaction.user, 'roles'):
            for role in interaction.user.roles:
                if str(role.id) in bank.config.get('work_roles', {}):
                    current_role = bank.config['work_roles'][str(role.id)]
                    # Compare roles based on max possible earnings in knuts
                    current_max_knuts = current_role['max']
                    if current_role['currency'] == 'galleon':
                        current_max_knuts *= bank.KNUTS_PER_GALLEON
                    elif current_role['currency'] == 'sickle':
                        current_max_knuts *= bank.KNUTS_PER_SICKLE
                        
                    work_max_knuts = work_config['max']
                    if work_config['currency'] == 'galleon':
                        work_max_knuts *= bank.KNUTS_PER_GALLEON
                    elif work_config['currency'] == 'sickle':
                        work_max_knuts *= bank.KNUTS_PER_SICKLE
                        
                    if current_max_knuts > work_max_knuts:
                        work_config = current_role
        else:
            logger.warning(f"User {user_id} ({username}) doesn't have roles attribute - using default work config")
        
        # Generate earnings based on role
        amount = random.randint(work_config['min'], work_config['max'])
        
        # Convert to knuts
        if work_config['currency'] == 'galleon':
            knuts_earned = amount * bank.KNUTS_PER_GALLEON
            currency_name = 'Galleons'
        elif work_config['currency'] == 'sickle':
            knuts_earned = amount * bank.KNUTS_PER_SICKLE
            currency_name = 'Sickles'
        else:
            knuts_earned = amount
            currency_name = 'Knuts'
        
        # Use a transaction to ensure both balance update and cooldown are set atomically
        with bank.get_db_connection() as conn:
            cursor = conn.cursor()
            try:
                cursor.execute('BEGIN TRANSACTION')
                
                # Update balance
                bank.update_balance(user_id, knuts_earned, username)
                # Set cooldown
                bank.set_cooldown(user_id, 'work_cooldown', current_time.isoformat())
                
                conn.commit()
                logger.info(f"Work: User {user_id} ({username}) earned {knuts_earned} knuts")
            except Exception as e:
                conn.rollback()
                logger.error(f"Failed to process work command: {e}")
                raise
        
        # Schedule notification for VIP users
        if bank.is_vip(user_id) and vip_notifications:
            cooldown_end = current_time + timedelta(hours=4)
            await vip_notifications.schedule_notification(user_id, "work", cooldown_end)
        
        current_balance = bank.get_balance(user_id)
        work_quote = random.choice(bank.work_quotes)
        
        embed = Embed(
            title=f"{bank.work_emoji} Work Complete!",
            description=work_quote,
            color=bank.success_color
        )
        embed.add_field(
            name="Earnings",
            value=f"**{amount}** {currency_name}\n({bank.format_currency(knuts_earned)})",
            inline=False
        )
        embed.add_field(
            name="New Balance",
            value=bank.format_currency(current_balance),
            inline=False
        )
        embed.set_footer(text=f"Come back in 4 hours to work again!")
        
        # Log transaction
        try:
            bank.log_transaction(
                user_id, 
                knuts_earned, 
                'work',
                str(interaction.user.id),
                f"Work earnings: {amount} {currency_name}"
            )
            
            # After successful work, create and send log
            log_embed = Embed(
                title="Work Activity Log",
                description=f"{interaction.user.mention} worked and earned {bank.format_currency(knuts_earned)}",
                color=bank.info_color,
                timestamp=datetime.now()
            )
            log_embed.set_footer(text=f"User ID: {interaction.user.id}")
            
            # Send to log channel - wrapped in try/except
            try:
                await bank.log_to_channel(bot, log_embed)
            except Exception as e:
                logger.error(f"Failed to log work activity: {e}")
        except Exception as e:
            logger.error(f"Error logging transaction: {e}")
        
        # Send response using rate-limited helper
        await send_message(interaction, embed=embed)
        
    except Exception as e:
        logger.error(f"Error in work command: {e}")
        error_embed = Embed(
            title="❌ Error",
            description="An error occurred while processing your work request.",
            color=bank.error_color
        )
        await send_message(interaction, embed=error_embed, ephemeral=True)

@bot.tree.command(name="collect_income", description="Collect weekly income based on your role")
@app_commands.guild_only()
@custom_cooldown(1, 86400, "user")  # 1 use per day (86400 seconds)
async def collect_income(interaction: discord.Interaction):
    try:
        # Defer response to prevent timeout
        await defer_response(interaction)
        
        user_id = str(interaction.user.id)
        username = interaction.user.display_name
        current_time = datetime.now()
        
        # Check cooldown
        last_income = bank.get_cooldown(user_id, 'income_cooldown')
        if last_income:
            last_income = datetime.fromisoformat(last_income)
            if current_time < last_income + timedelta(weeks=1):
                time_remaining = (last_income + timedelta(weeks=1) - current_time)
                days_remaining = time_remaining.days
                seconds_remaining = time_remaining.seconds
                hours_remaining = seconds_remaining // 3600
                minutes_remaining = (seconds_remaining % 3600) // 60
                seconds_display = seconds_remaining % 60
                
                embed = Embed(
                    title=f"{bank.time_emoji} Income Cooldown",
                    description=f"You must wait **{days_remaining}** days, **{hours_remaining}** hours, **{minutes_remaining}** minutes and **{seconds_display}** seconds before collecting again!",
                    color=bank.error_color
                )
                
                # Show VIP promotion if not a VIP
                if not bank.is_vip(user_id):
                    embed.add_field(
                        name=f"{bank.vip_emoji} VIP Benefit",
                        value="VIP members receive notifications when cooldowns expire! Use `/buy_vip` to purchase.",
                        inline=False
                    )
                
                await send_message(interaction, embed=embed)
                return
        
        # Find highest paying role
        highest_income = None
        highest_role = None
        highest_config = None

        # Ensure we have a proper Member object with roles
        member = None
        if interaction.guild:
            # First try to use interaction.user if it already has roles
            if hasattr(interaction.user, 'roles'):
                member = interaction.user
            else:
                # If interaction.user doesn't have roles, fetch the Member object
                try:
                    member = interaction.guild.get_member(interaction.user.id)
                    if not member:
                        # If not in cache, fetch from Discord API
                        member = await interaction.guild.fetch_member(interaction.user.id)
                    logger.info(f"Fetched Member object for user {user_id} ({username})")
                except discord.NotFound:
                    logger.warning(f"User {user_id} ({username}) not found in guild - using default income")
                except discord.HTTPException as e:
                    logger.error(f"Error fetching member {user_id} ({username}): {e} - using default income")
                except Exception as e:
                    logger.error(f"Unexpected error fetching member {user_id} ({username}): {e} - using default income")

        # Process roles if we have a valid member
        if member and hasattr(member, 'roles'):
            logger.info(f"Processing roles for user {user_id} ({username}): {len(member.roles)} roles found")
            for role in member.roles:
                role_id_str = str(role.id)
                if role_id_str in bank.config.get('income_roles', {}):
                    current_role = bank.config['income_roles'][role_id_str]
                    current_knuts = current_role['amount']

                    if current_role['currency'] == 'galleon':
                        current_knuts *= bank.KNUTS_PER_GALLEON
                    elif current_role['currency'] == 'sickle':
                        current_knuts *= bank.KNUTS_PER_SICKLE

                    if highest_income is None or current_knuts > highest_income:
                        highest_income = current_knuts
                        highest_role = role
                        highest_config = current_role
                        logger.info(f"Found income role for user {user_id}: {role.name} ({role_id_str}) = {current_knuts} knuts")
        else:
            if not interaction.guild:
                logger.warning(f"User {user_id} ({username}) used income command outside of guild - using default income")
            else:
                logger.warning(f"User {user_id} ({username}) - could not get Member object with roles - using default income")
        
        # If no role-based income, use default
        if highest_income is None:
            default_config = bank.config.get('default_income', {'amount': 1, 'currency': 'galleon'})
            highest_income = default_config['amount']
            if default_config['currency'] == 'galleon':
                highest_income *= bank.KNUTS_PER_GALLEON
            elif default_config['currency'] == 'sickle':
                highest_income *= bank.KNUTS_PER_SICKLE
            highest_config = default_config
            logger.info(f"User {user_id} ({username}) receiving default income: {highest_income} knuts")
        else:
            logger.info(f"User {user_id} ({username}) receiving role-based income: {highest_income} knuts from role {highest_role.name if highest_role else 'unknown'}")
        
        # Update balance and set cooldown
        bank.update_balance(user_id, highest_income, username)
        bank.set_cooldown(user_id, 'income_cooldown', current_time.isoformat())
        
        # Schedule notification for VIP users
        if bank.is_vip(user_id) and vip_notifications:
            cooldown_end = current_time + timedelta(weeks=1)
            await vip_notifications.schedule_notification(user_id, "income", cooldown_end)
        
        # Get new balance
        new_balance = bank.get_balance(user_id)
        
        embed = Embed(
            title=f"{bank.bank_emoji} Weekly Income Collected!",
            color=bank.success_color
        )
        
        if highest_role:
            embed.add_field(
                name="Role Income",
                value=f"Role: {highest_role.mention}\n"
                      f"Amount: **{highest_config['amount']}** {highest_config['currency'].title()}s\n"
                      f"({bank.format_currency(highest_income)})",
                inline=False
            )
        else:
            embed.add_field(
                name="Basic Income",
                value=f"Amount: **{highest_config['amount']}** {highest_config['currency'].title()}s\n"
                      f"({bank.format_currency(highest_income)})",
                inline=False
            )
        
        embed.add_field(
            name="New Balance",
            value=bank.format_currency(new_balance),
            inline=False
        )
        embed.set_footer(text="Come back next week to collect again!")
        
        # Log transaction with try/except
        try:
            bank.log_transaction(
                user_id, 
                highest_income, 
                'income',
                str(interaction.user.id),
                f"Weekly income - {'Role: ' + highest_role.name if highest_role else 'Default'}"
            )
            
            # Create log embed 
            log_embed = Embed(
                title="💰 Income Collected",
                description=f"{interaction.user.mention} collected their weekly income",
                color=bank.info_color,
                timestamp=datetime.now()
            )
            
            if highest_role:
                log_embed.add_field(
                    name="Role Income",
                    value=f"Role: {highest_role.mention}\n"
                          f"Amount: **{highest_config['amount']}** {highest_config['currency'].title()}s\n"
                          f"({bank.format_currency(highest_income)})",
                    inline=False
                )
            else:
                log_embed.add_field(
                    name="Basic Income",
                    value=f"Amount: **{highest_config['amount']}** {highest_config['currency'].title()}s\n"
                          f"({bank.format_currency(highest_income)})",
                    inline=False
                )
            
            log_embed.set_footer(text=f"User ID: {interaction.user.id}")
            
            # Send to log channel with try/except
            try:
                await bank.log_to_channel(bot, log_embed)
            except Exception as log_error:
                print(f"Error sending income log: {log_error}")
                # Continue even if logging fails
        except Exception as tx_error:
            print(f"Error in transaction logging: {tx_error}")
            # Continue even if transaction logging fails
        
        # Send response using followup since we deferred earlier
        await interaction.followup.send(embed=embed)
        
    except Exception as e:
        print(f"Error in collect_income command: {e}")
        error_embed = Embed(
            title="❌ Error",
            description="An error occurred while processing your income collection.",
            color=Color.red()
        )
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(embed=error_embed, ephemeral=True)
            else:
                await interaction.followup.send(embed=error_embed, ephemeral=True)
        except Exception as follow_error:
            print(f"Error sending error message: {follow_error}")

@bot.tree.command(name="remove_item", description="Remove an item from the shop (Shop Managers only)")
@app_commands.guild_only()
async def remove_item(interaction: discord.Interaction, category: str):
    # Check if user has shop manager role
    has_permission = False
    for role in interaction.user.roles:
        if role.id in bank.config['shop_manager_roles']:
            has_permission = True
            break
    
    if not has_permission and interaction.user.id != bot.owner_id:
        await interaction.response.send_message(
            "You don't have permission to remove shop items!",
            ephemeral=True
        )
        return
    
    with bank.get_db_connection() as conn:
        c = conn.cursor()
        # Get items from shop
        c.execute('''SELECT id, name, price, description 
                    FROM shop_items 
                    WHERE category = ?
                    ORDER BY name''', (category,))
        items = c.fetchall()
    
    if not items:
        await interaction.response.send_message(
            f"No items found in category '{category}'!",
            ephemeral=True
        )
        return
    
    # Create paginated item removal view
    view = PaginatedRemoveItemView(items, category)
    embed = view.create_embed()

    await interaction.response.send_message(
        embed=embed,
        view=view,
        ephemeral=True
    )

class PaginatedRemoveItemView(View):
    def __init__(self, items, category):
        super().__init__(timeout=60)
        self.items = items
        self.category = category
        self.current_page = 0
        self.items_per_page = 25
        self.total_pages = (len(items) - 1) // self.items_per_page + 1

        self.update_components()

    def get_current_items(self):
        start = self.current_page * self.items_per_page
        end = start + self.items_per_page
        return self.items[start:end]

    def create_embed(self):
        embed = Embed(
            title=f"🗑️ Remove Item from {self.category}",
            description="Select an item to remove from the shop:",
            color=bank.info_color
        )

        if self.total_pages > 1:
            embed.add_field(
                name="📄 Page Info",
                value=f"Page {self.current_page + 1} of {self.total_pages} ({len(self.items)} total items)",
                inline=False
            )

        return embed

    def update_components(self):
        self.clear_items()

        # Add item select
        current_items = self.get_current_items()
        if current_items:
            self.add_item(RemoveItemSelect(current_items))

        # Add pagination buttons if needed
        if self.total_pages > 1:
            self.add_item(RemovePreviousButton(disabled=self.current_page == 0))
            self.add_item(RemoveNextButton(disabled=self.current_page == self.total_pages - 1))

class RemoveItemSelect(Select):
    def __init__(self, items):
        options = []
        for item_id, name, price, description in items:
            # Create shorter price description
            galleons = price // bank.KNUTS_PER_GALLEON
            remaining = price % bank.KNUTS_PER_GALLEON
            sickles = remaining // bank.KNUTS_PER_SICKLE
            knuts = remaining % bank.KNUTS_PER_SICKLE

            price_text = []
            if galleons > 0:
                price_text.append(f"{galleons}G")
            if sickles > 0:
                price_text.append(f"{sickles}S")
            if knuts > 0:
                price_text.append(f"{knuts}K")
            price_desc = " ".join(price_text) or "0K"

            options.append(
                SelectOption(
                    label=name,
                    value=str(item_id),
                    description=price_desc
                )
            )

        super().__init__(
            placeholder="Choose an item to remove from shop",
            options=options
        )

    async def callback(self, interaction: discord.Interaction):
        with bank.get_db_connection() as conn:
            c = conn.cursor()
            # Get item details
            c.execute('''SELECT id, name, price, description
                        FROM shop_items
                        WHERE id = ?''', (int(self.values[0]),))
            item = c.fetchone()

            if not item:
                await interaction.response.send_message(
                    "Item not found! It may have been already removed.",
                    ephemeral=True
                )
                return

            item_id, name, price, description = item

            # Check how many players own this item
            c.execute('SELECT COUNT(*) FROM inventory WHERE item_id = ?', (item_id,))
            owned_count = c.fetchone()[0]

            # Create confirmation embed
            embed = Embed(
                title="❌ Remove from Shop?",
                description=f"Are you sure you want to remove **{name}** from the shop?\n\n"
                           f"**Category:** {self.view.category}\n"
                           f"**Price:** {bank.format_currency(price)}\n"
                           f"**Description:** {description or 'None'}\n"
                           f"**Currently owned by:** {owned_count} users\n\n"
                           "Note: Players who own this item will keep it in their inventory.",
                color=bank.error_color
            )

            # Create confirmation buttons
            class ConfirmButtons(View):
                def __init__(self):
                    super().__init__(timeout=60)
                    
                    @discord.ui.button(label="Remove", style=discord.ButtonStyle.danger)
                    async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
                        try:
                            with bank.get_db_connection() as conn:
                                c = conn.cursor()
                                try:
                                    # First get all item details
                                    c.execute('''SELECT * FROM shop_items WHERE id = ?''', (item_id,))
                                    item_data = c.fetchone()
                                    
                                    if not item_data:
                                        await interaction.response.edit_message(
                                            content="Item not found! It may have been already removed.",
                                            view=None
                                        )
                                        return

                                    # Move item to removed_shop_items
                                    c.execute('''INSERT INTO removed_shop_items 
                                               (id, name, price, category, description, properties, added_by)
                                               VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                            (item_data['id'], item_data['name'], item_data['price'],
                                             item_data['category'], item_data['description'],
                                             item_data['properties'], item_data['added_by']))

                                    # Update inventory records to mark them as removed
                                    c.execute('''UPDATE inventory 
                                               SET is_removed_item = 1 
                                               WHERE item_id = ?''', (item_id,))

                                    # Now delete from shop_items
                                    c.execute('DELETE FROM shop_items WHERE id = ?', (item_id,))

                                    # Create log embed
                                    log_embed = Embed(
                                        title="🗑️ Shop Item Removed",
                                        description=f"**{name}** was removed from the shop by {interaction.user.mention}",
                                        color=bank.info_color,
                                        timestamp=datetime.now()
                                    )
                                    log_embed.add_field(
                                        name="Item Details",
                                        value=f"Category: {category}\nPrice: {bank.format_currency(price)}",
                                        inline=False
                                    )
                                    log_embed.add_field(
                                        name="Current Owners",
                                        value=f"{owned_count} players keep their items",
                                        inline=False
                                    )
                                    log_embed.set_footer(text=f"Removed by: {interaction.user.id}")
                                    
                                    # Send to log channel
                                    await bank.log_to_channel(bot, log_embed)
                                    
                                    result_embed = Embed(
                                        title="✅ Item Removed from Shop",
                                        description=f"**{name}** has been removed from the shop.\n"
                                                   f"All {owned_count} current owners keep their items.",
                                        color=bank.success_color
                                    )
                                    await interaction.response.edit_message(
                                        embed=result_embed,
                                        view=None
                                    )
                                    
                                except Exception as e:
                                    print(f"Error removing item: {e}")
                                    await interaction.response.edit_message(
                                        content="An error occurred while removing the item.",
                                        view=None
                                    )
                        except Exception as e:
                            print(f"Error removing item: {e}")
                            await interaction.response.edit_message(
                                content="An error occurred while removing the item.",
                                view=None
                            )
                
                    @discord.ui.button(label="Cancel", style=discord.ButtonStyle.grey)
                    async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
                        # Restrict to original user
                        if interaction.user.id != self.original_user_id:
                            await interaction.response.send_message(
                                "You are not allowed to cancel this purchase.", ephemeral=True
                            )
                            return
                        # Disable all buttons immediately to prevent multiple submissions
                        for item in self.children:
                            item.disabled = True
                        cancel_embed = Embed(
                            title="❌ Purchase Cancelled",
                            description="Your purchase has been cancelled.",
                            color=bank.error_color
                        )
                        try:
                            await interaction.response.edit_message(
                                embed=cancel_embed,
                                view=self
                            )
                        except:
                            try:
                                await interaction.followup.edit_message(
                                    interaction.message.id,
                                    embed=cancel_embed,
                                    view=self
                                )
                            except:
                                await interaction.followup.send(
                                    embed=cancel_embed,
                                    ephemeral=False
                                )
                
                await interaction.response.edit_message(
                    embed=embed,
                    view=ConfirmButtons()
                )

class RemovePreviousButton(Button):
    def __init__(self, disabled=False):
        super().__init__(label="◀ Previous", style=discord.ButtonStyle.secondary, disabled=disabled)

    async def callback(self, interaction: discord.Interaction):
        view = self.view
        if view.current_page > 0:
            view.current_page -= 1
            view.update_components()
            embed = view.create_embed()
            await interaction.response.edit_message(embed=embed, view=view)

class RemoveNextButton(Button):
    def __init__(self, disabled=False):
        super().__init__(label="Next ▶", style=discord.ButtonStyle.secondary, disabled=disabled)

    async def callback(self, interaction: discord.Interaction):
        view = self.view
        if view.current_page < view.total_pages - 1:
            view.current_page += 1
            view.update_components()
            embed = view.create_embed()
            await interaction.response.edit_message(embed=embed, view=view)

@remove_item.autocomplete('category')
async def remove_category_autocomplete(interaction: discord.Interaction, current: str):
    with bank.get_db_connection() as conn:
        c = conn.cursor()
        c.execute('SELECT DISTINCT category FROM shop_items')
        categories = [row[0] for row in c.fetchall()]
    
    return [
        app_commands.Choice(name=cat, value=cat)
        for cat in categories
        if current.lower() in cat.lower()
    ][:25]

@bot.tree.command(name="craft_accessories", description="Create a custom accessory (Shop Managers only)")
@app_commands.guild_only()
@app_commands.describe(
    name="Name of the accessory",
    price="Amount to set as price",
    currency="Currency type (Galleons, Sickles, or Knuts)",
    material="Material of the accessory (e.g., Gold, Silver, Bronze)",
    type="Type of accessory (e.g., Necklace, Ring, Bracelet)",
    enchantment="Magical property of the accessory",
    description="Description of the accessory",
    required_role="Role ID required to purchase this accessory (optional)"
)
async def craft_accessories(
    interaction: discord.Interaction,
    name: str,
    price: int,
    currency: Literal["Galleons", "Sickles", "Knuts"],
    material: str,
    type: str,
    enchantment: str,
    description: str,
    required_role: Optional[str] = None
):
    # Use deferred response to prevent timeout
    await interaction.response.defer()
    
    # Check if user has shop manager role
    has_permission = False
    for role in interaction.user.roles:
        if role.id in bank.config['shop_manager_roles']:
            has_permission = True
            break
    
    if not has_permission and interaction.user.id != bot.owner_id:
        await interaction.followup.send(
            "You don't have permission to craft accessories!",
            ephemeral=True
        )
        return
    
    # Convert to knuts
    if currency == "Galleons":
        price_in_knuts = price * bank.KNUTS_PER_GALLEON
    elif currency == "Sickles":
        price_in_knuts = price * bank.KNUTS_PER_SICKLE
    else:
        price_in_knuts = price
    
    # Create accessory properties
    properties = {
        "material": material,
        "type": type,
        "enchantment": enchantment
    }
    
    with bank.get_db_connection() as conn:
        c = conn.cursor()
        try:
            c.execute('''INSERT INTO shop_items 
                        (name, price, category, description, properties, added_by, required_role)
                        VALUES (?, ?, ?, ?, ?, ?, ?)''',
                     (name, 
                      price_in_knuts, 
                      "Accessories",
                      description,
                      json.dumps(properties),
                      str(interaction.user.id),
                      required_role))
            
            conn.commit()
            
            # Create confirmation embed
            embed = Embed(
                title="✨ New Accessory Created",
                description=f"Added **{name}** to the shop",
                color=bank.success_color
            )
            
            embed.add_field(
                name="Price",
                value=bank.format_currency(price_in_knuts),
                inline=False
            )
            
            embed.add_field(
                name="Properties",
                value=f"Material: {material}\n"
                      f"Type: {type}\n"
                      f"Enchantment: {enchantment}",
                inline=False
            )
            
            if description:
                embed.add_field(
                    name="Description",
                    value=description,
                    inline=False
                )
            
            await interaction.followup.send(embed=embed)
            
            # After successful accessory creation, before sending response
            log_embed = Embed(
                title="✨ New Accessory Created",
                description=f"{interaction.user.mention} created a new accessory",
                color=bank.info_color,
                timestamp=datetime.now()
            )
            log_embed.add_field(
                name="Accessory Details",
                value=f"**{name}**\nPrice: {bank.format_currency(price_in_knuts)}",
                inline=False
            )
            log_embed.add_field(
                name="Properties",
                value=f"Material: {material}\n"
                      f"Type: {type}\n"
                      f"Enchantment: {enchantment}",
                inline=False
            )
            if description:
                log_embed.add_field(
                    name="Description",
                    value=description,
                    inline=False
                )
            log_embed.set_footer(text=f"Created by: {interaction.user.id}")
            
            # Send to log channel
            await bank.log_to_channel(bot, log_embed)
            
            # Update the log embed to include required role
            log_embed.add_field(
                name="Required Role",
                value=f"<@&{required_role}>" if required_role else "None",
                inline=False
            )
            
        except Exception as e:
            conn.rollback()
            print(f"Error creating accessory: {e}")
            await interaction.response.send_message(
                "An error occurred while creating the accessory.",
                ephemeral=True
            )

@bot.tree.command(name="leaderboard", description="Show various leaderboards")
@app_commands.guild_only()
async def leaderboard(interaction: discord.Interaction, category: Literal["wealth", "transactions", "income"]):
    # Check if user has banker role
    has_permission = False
    for role in interaction.user.roles:
        if role.id in bank.banker_roles:
            has_permission = True
            break
    
    if not has_permission and interaction.user.id != bot.owner_id:
        await interaction.response.send_message(
            "You don't have permission to use this command! Only Bank Masters can view leaderboards.",
            ephemeral=True
        )
        return

    with bank.get_db_connection() as conn:
        c = conn.cursor()
        
        if category == "wealth":
            c.execute('''
                SELECT username, (galleons * ? + sickles * ? + knuts) as total
                FROM users 
                ORDER BY total DESC LIMIT 10
            ''', (bank.KNUTS_PER_GALLEON, bank.KNUTS_PER_SICKLE))
            title = "🏆 Wealthiest Users"
        
        elif category == "transactions":
            c.execute('''
                SELECT u.username, COUNT(*) as count
                FROM transactions t
                JOIN users u ON t.user_id = u.user_id
                GROUP BY t.user_id
                ORDER BY count DESC LIMIT 10
            ''')
            title = "🔄 Most Active Users"
        
        else:  # income
            c.execute('''
                SELECT u.username, SUM(amount) as total
                FROM transactions t
                JOIN users u ON t.user_id = u.user_id
                WHERE type = 'income'
                GROUP BY t.user_id
                ORDER BY total DESC LIMIT 10
            ''')
            title = "💰 Highest Earners"
        
        results = c.fetchall()

    embed = Embed(title=title, color=bank.info_color)
    
    for i, (name, value) in enumerate(results, 1):
        if category == "transactions":
            embed.add_field(
                name=f"#{i} {name}",
                value=f"**{value}** transactions",
                inline=False
            )
        else:
            embed.add_field(
                name=f"#{i} {name}",
                value=bank.format_currency(value),
                inline=False
            )

    await interaction.response.send_message(embed=embed)  # Removed ephemeral=True to make it visible to all

@bot.tree.command(name="use", description="Use an item from your inventory")
@app_commands.guild_only()
async def use(interaction: discord.Interaction):
    try:
        # Use deferred response to prevent timeout
        await interaction.response.defer(ephemeral=True)
        
        user_id = str(interaction.user.id)
        
        # Get usable items from inventory (excluding main equipment)
        with bank.get_db_connection() as conn:
            c = conn.cursor()
            c.execute('''
                SELECT i.id, 
                       COALESCE(s.name, r.name) as name,
                       COALESCE(s.description, r.description) as description,
                       COALESCE(s.category, r.category) as category,
                       COALESCE(s.properties, r.properties) as properties
                FROM inventory i
                LEFT JOIN shop_items s ON i.item_id = s.id AND i.is_removed_item = 0
                LEFT JOIN removed_shop_items r ON i.item_id = r.id AND i.is_removed_item = 1
                WHERE i.user_id = ? 
                AND COALESCE(s.category, r.category) NOT IN ('Wands', 'Brooms', 'Accessories')
            ''', (user_id,))
            
            items = c.fetchall()
        
        if not items:
            await interaction.followup.send(
                "You don't have any usable items in your inventory!",
                ephemeral=True
            )
            return
    
        # Create paginated use item view
        view = PaginatedUseItemView(items)
        embed = view.create_embed()

        await interaction.followup.send(
            embed=embed,
            view=view,
            ephemeral=True
        )

    except Exception as e:
        print(f"Error in use command: {e}")
        await interaction.followup.send(
            "An error occurred while loading your inventory.",
            ephemeral=True
        )

class PaginatedUseItemView(View):
    def __init__(self, items):
        super().__init__(timeout=60)
        self.items = items
        self.current_page = 0
        self.items_per_page = 25
        self.total_pages = (len(items) - 1) // self.items_per_page + 1

        self.update_components()

    def get_current_items(self):
        start = self.current_page * self.items_per_page
        end = start + self.items_per_page
        return self.items[start:end]

    def create_embed(self):
        embed = Embed(
            title="🎯 Use Item",
            description="Select an item to use from your inventory:",
            color=bank.info_color
        )

        if self.total_pages > 1:
            embed.add_field(
                name="📄 Page Info",
                value=f"Page {self.current_page + 1} of {self.total_pages} ({len(self.items)} total items)",
                inline=False
            )

        return embed

    def update_components(self):
        self.clear_items()

        # Add item select
        current_items = self.get_current_items()
        if current_items:
            self.add_item(UseItemSelect(current_items, self.items))

        # Add pagination buttons if needed
        if self.total_pages > 1:
            self.add_item(UsePreviousButton(disabled=self.current_page == 0))
            self.add_item(UseNextButton(disabled=self.current_page == self.total_pages - 1))

class UseItemSelect(Select):
    def __init__(self, current_items, all_items):
        self.all_items = all_items
        options = []
        for item_id, name, description, category, _ in current_items:
            options.append(
                SelectOption(
                    label=name[:100],
                    value=str(item_id),
                    description=f"{category}: {description[:100] if description else 'No description'}"
                )
            )

        super().__init__(
            placeholder="Choose an item to use",
            options=options,
            min_values=1,
            max_values=1
        )

    async def callback(self, interaction: discord.Interaction):
        # Defer this response to avoid interaction timeouts
        await interaction.response.defer(ephemeral=True)

        selected_id = int(self.values[0])
        selected_item = next(item for item in self.all_items if item[0] == selected_id)
        item_id, name, description, category, properties = selected_item

        # Create confirmation embed
        embed = Embed(
            title="🎯 Use Item?",
            description=f"Are you sure you want to use **{name}**?\n\n" \
                       f"Category: {category}\n" \
                       f"*{description if description else 'No description'}*\n\n" \
                       "This item will be consumed upon use.",
            color=bank.info_color
        )

        # Create confirmation buttons
        class ConfirmButtons(View):
            def __init__(self):
                super().__init__(timeout=60)

            @discord.ui.button(label="Use", style=discord.ButtonStyle.primary)
            async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
                        try:
                            with bank.get_db_connection() as conn:
                                c = conn.cursor()
                                # Remove item from inventory
                                c.execute('DELETE FROM inventory WHERE id = ?', (item_id,))
                                conn.commit()
                            
                            # Create success embed
                            result_embed = Embed(
                                title="✨ Item Used",
                                description=f"You used your **{name}**!",
                                color=bank.success_color
                            )
                            
                            # Create log embed
                            log_embed = Embed(
                                title="🎯 Item Usage Log",
                                description=f"{interaction.user.mention} used an item",
                                color=bank.info_color,
                                timestamp=datetime.now()
                            )
                            log_embed.add_field(
                                name="Item Used",
                                value=f"**{name}**\nCategory: {category}",
                                inline=False
                            )
                            if description:
                                log_embed.add_field(
                                    name="Description",
                                    value=description,
                                    inline=False
                                )
                            log_embed.set_footer(text=f"User ID: {interaction.user.id}")
                            
                            # Send to log channel
                            await bank.log_to_channel(bot, log_embed)
                            
                            await interaction.response.edit_message(
                                embed=result_embed,
                                view=None
                            )
                            
                        except Exception as e:
                            print(f"Error using item: {e}")
                            error_embed = Embed(
                                title="❌ Error",
                                description="An error occurred while using the item.",
                                color=bank.error_color
                            )
                            await interaction.response.edit_message(
                                embed=error_embed,
                                view=None
                            )

                @discord.ui.button(label="Cancel", style=discord.ButtonStyle.secondary)
                async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
                    cancel_embed = Embed(
                        title="❌ Cancelled",
                        description="Item use cancelled.",
                        color=bank.info_color
                    )
                    await interaction.response.edit_message(
                        embed=cancel_embed,
                        view=None
                    )

        await interaction.followup.send(
            embed=embed,
            view=ConfirmButtons(),
            ephemeral=True
        )

class UsePreviousButton(Button):
    def __init__(self, disabled=False):
        super().__init__(label="◀ Previous", style=discord.ButtonStyle.secondary, disabled=disabled)

    async def callback(self, interaction: discord.Interaction):
        view = self.view
        if view.current_page > 0:
            view.current_page -= 1
            view.update_components()
            embed = view.create_embed()
            await interaction.response.edit_message(embed=embed, view=view)

class UseNextButton(Button):
    def __init__(self, disabled=False):
        super().__init__(label="Next ▶", style=discord.ButtonStyle.secondary, disabled=disabled)

    async def callback(self, interaction: discord.Interaction):
        view = self.view
        if view.current_page < view.total_pages - 1:
            view.current_page += 1
            view.update_components()
            embed = view.create_embed()
            await interaction.response.edit_message(embed=embed, view=view)

@bot.tree.command(name="shop", description="Browse and buy items from the shop")
@app_commands.guild_only()
async def shop(interaction: discord.Interaction):
    try:
        # Use deferred response to prevent timeout
        await interaction.response.defer(ephemeral=True)
        
        with bank.get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT DISTINCT category FROM shop_items ORDER BY category')
            categories = c.fetchall()
            
            if not categories:
                await interaction.followup.send("No items in shop yet!", ephemeral=False)
                return

            # Create category selection menu
            options = []
            for cat in categories:
                c.execute('SELECT COUNT(*) FROM shop_items WHERE category = ?', (cat[0],))
                count = c.fetchone()[0]
                options.append(
                    SelectOption(
                        label=cat[0],
                        value=cat[0],
                        description=f"{count} items available"
                    )
                )

            class ShopView(View):
                def __init__(self):
                    super().__init__(timeout=60)
                    self.add_item(CategorySelect(options))

            class PaginatedShopView(View):
                def __init__(self, items, category):
                    super().__init__(timeout=60)
                    self.items = items
                    self.category = category
                    self.current_page = 0
                    self.items_per_page = 25
                    self.total_pages = (len(items) - 1) // self.items_per_page + 1

                    self.update_components()

                def get_current_items(self):
                    start = self.current_page * self.items_per_page
                    end = start + self.items_per_page
                    return self.items[start:end]

                def create_embed(self):
                    embed = Embed(
                        title=f"🛍️ Shop - {self.category}",
                        description="Select an item to purchase:",
                        color=bank.info_color
                    )

                    if self.total_pages > 1:
                        embed.add_field(
                            name="📄 Page Info",
                            value=f"Page {self.current_page + 1} of {self.total_pages} ({len(self.items)} total items)",
                            inline=False
                        )

                    # Add items list to embed
                    current_items = self.get_current_items()
                    items_text = []
                    for item in current_items:
                        price_text = bank.format_currency_short(item[2])
                        items_text.append(f"**{item[1]}** - {price_text}")
                        if item[3]:  # If there's a description
                            items_text.append(f"*{item[3]}*")
                        items_text.append("")  # Add blank line between items

                    if items_text:
                        embed.description = "Select an item to purchase:\n\n" + "\n".join(items_text)

                    return embed

                def update_components(self):
                    self.clear_items()

                    # Add item select
                    current_items = self.get_current_items()
                    options = []
                    for item in current_items:
                        price_text = bank.format_currency_short(item[2])
                        options.append(
                            SelectOption(
                                label=f"{item[1]} ({price_text})",
                                value=str(item[0]),
                                description=item[3][:100] if item[3] else "No description"
                            )
                        )

                    if options:
                        self.add_item(ItemSelect(options))

                    # Add pagination buttons if needed
                    if self.total_pages > 1:
                        self.add_item(PreviousButton(disabled=self.current_page == 0))
                        self.add_item(NextButton(disabled=self.current_page == self.total_pages - 1))

            class ItemSelect(Select):
                def __init__(self, options):
                    super().__init__(
                        placeholder="Choose an item to buy",
                        options=options
                    )

                async def callback(self, interaction: discord.Interaction):
                    try:
                        # Defer this response to prevent timeouts during purchase processing
                        await interaction.response.defer(ephemeral=True)

                        item_id = int(self.values[0])

                        # Get item details first
                        with bank.get_db_connection() as conn:
                            c = conn.cursor()
                            c.execute('''SELECT name, price, category, description, required_role
                                        FROM shop_items
                                        WHERE id = ?''', (item_id,))
                            item = c.fetchone()

                            if not item:
                                await interaction.followup.send("Item not found!", ephemeral=False)
                                return

                            name, price, category, description, required_role = item

                            # Check if user can afford the item
                            user_balance = bank.get_balance(str(interaction.user.id))
                            if user_balance < price:
                                await interaction.followup.send(
                                    f"You cannot afford this item! Price: {bank.format_currency(price)}",
                                    ephemeral=False
                                )
                                return

                            # Check required role if any
                            if required_role:
                                has_role = False
                                # Parse the required role to handle different input formats
                                required_role_id = bank.parse_role_input(required_role, interaction.guild)

                                for role in interaction.user.roles:
                                    if str(role.id) == required_role_id:
                                        has_role = True
                                        break
                                if not has_role:
                                    await interaction.followup.send(
                                        f"You need the <@&{required_role_id}> role to buy this item!",
                                        ephemeral=False
                                    )
                                    return

                            # Check if user already has an item of this category (for Wands and Brooms)
                            if category in ["Wands", "Brooms"]:
                                c.execute('''
                                    SELECT COUNT(*) FROM inventory i
                                    JOIN shop_items s ON i.item_id = s.id
                                    WHERE i.user_id = ?
                                    AND s.category = ?
                                    AND i.is_removed_item = 0
                                ''', (str(interaction.user.id), category))

                                if c.fetchone()[0] > 0:
                                    item_type = category[:-1]  # Remove the 's' to get singular form
                                    await interaction.followup.send(
                                        f"You already own a {item_type.lower()}! You must destroy your current one before buying another.\n\nUse the `/destroy` command to destroy your current {item_type.lower()} first.",
                                        ephemeral=False
                                    )
                                    return

                        # Create confirmation buttons
                        class ConfirmPurchase(View):
                            def __init__(self, original_user_id):
                                super().__init__(timeout=60)
                                self.original_user_id = original_user_id

                            @discord.ui.button(label="Confirm Purchase", style=discord.ButtonStyle.green)
                            async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
                                # Restrict to original user
                                if interaction.user.id != self.original_user_id:
                                    await interaction.response.send_message(
                                        "You are not allowed to confirm this purchase.", ephemeral=True
                                    )
                                    return
                                try:
                                    # Disable all buttons immediately to prevent multiple submissions
                                    for item in self.children:
                                        item.disabled = True
                                    await interaction.response.edit_message(view=self)

                                    # Process the purchase
                                    success = bank.process_purchase(
                                        str(interaction.user.id),
                                        item_id,
                                        category,
                                        price,
                                        name,
                                        description
                                    )

                                    if success:
                                        # Create success embed
                                        success_embed = Embed(
                                            title="✅ Purchase Successful!",
                                            description=f"{interaction.user.mention} bought {name} for {bank.format_currency_short(price)}!",
                                            color=bank.success_color
                                        )

                                        if description:
                                            success_embed.add_field(
                                                name="Item Description",
                                                value=description,
                                                inline=False
                                            )

                                        try:
                                            await interaction.followup.edit_message(
                                                interaction.message.id,
                                                embed=success_embed,
                                                view=None
                                            )
                                        except:
                                            await interaction.followup.send(
                                                embed=success_embed,
                                                ephemeral=False
                                            )
                                    else:
                                        # Check if failure is due to already having an item of same category
                                        if category in ["Wands", "Brooms"]:
                                            item_type = category[:-1]  # Remove the 's' to get singular form
                                            error_msg = f"You already own a {item_type.lower()}! You must destroy your current one before buying another.\n\nUse the `/destroy` command to destroy your current {item_type.lower()} first."
                                        else:
                                            error_msg = "You cannot afford this item or an error occurred during purchase."

                                        error_embed = Embed(
                                            title="❌ Purchase Failed",
                                            description=error_msg,
                                            color=bank.error_color
                                        )
                                        try:
                                            await interaction.followup.edit_message(
                                                interaction.message.id,
                                                embed=error_embed,
                                                view=None
                                            )
                                        except:
                                            await interaction.followup.send(
                                                embed=error_embed,
                                                ephemeral=False
                                            )

                                except Exception as e:
                                    print(f"Purchase error: {e}")
                                    error_embed = Embed(
                                        title="❌ Purchase Failed",
                                        description="An error occurred during purchase. Please try again.",
                                        color=bank.error_color
                                    )
                                    try:
                                        await interaction.followup.send(
                                            embed=error_embed,
                                            ephemeral=False
                                        )
                                    except Exception as e:
                                        print(f"Failed to send error message: {e}")

                            @discord.ui.button(label="Cancel", style=discord.ButtonStyle.grey)
                            async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
                                # Restrict to original user
                                if interaction.user.id != self.original_user_id:
                                    await interaction.response.send_message(
                                        "You are not allowed to cancel this purchase.", ephemeral=True
                                    )
                                    return
                                # Disable all buttons immediately to prevent multiple submissions
                                for item in self.children:
                                    item.disabled = True
                                cancel_embed = Embed(
                                    title="❌ Purchase Cancelled",
                                    description="Your purchase has been cancelled.",
                                    color=bank.error_color
                                )
                                try:
                                    await interaction.response.edit_message(
                                        embed=cancel_embed,
                                        view=self
                                    )
                                except:
                                    try:
                                        await interaction.followup.edit_message(
                                            interaction.message.id,
                                            embed=cancel_embed,
                                            view=self
                                        )
                                    except:
                                        await interaction.followup.send(
                                            embed=cancel_embed,
                                            ephemeral=False
                                        )

                        # Show purchase confirmation
                        confirm_embed = Embed(
                            title="🛍️ Confirm Purchase",
                            description=f"Are you sure you want to buy **{name}**?",
                            color=bank.info_color
                        )
                        confirm_embed.add_field(
                            name="Price",
                            value=f"{bank.format_currency_short(price)}\n({bank.format_currency(price)})",
                            inline=False
                        )
                        if description:
                            confirm_embed.add_field(
                                name="Description",
                                value=description,
                                inline=False
                            )

                        await interaction.followup.send(
                            embed=confirm_embed,
                            view=ConfirmPurchase(interaction.user.id),
                            ephemeral=False
                        )
                    except Exception as e:
                        print(f"Error in item select callback: {e}")
                        error_embed = Embed(
                            title="❌ Error",
                            description="An error occurred while processing your selection.",
                            color=bank.error_color
                        )
                        if not interaction.response.is_done():
                            await interaction.response.send_message(embed=error_embed, ephemeral=False)
                        else:
                            await interaction.followup.send(embed=error_embed, ephemeral=False)

            class PreviousButton(Button):
                def __init__(self, disabled=False):
                    super().__init__(label="◀ Previous", style=discord.ButtonStyle.secondary, disabled=disabled)

                async def callback(self, interaction: discord.Interaction):
                    view = self.view
                    if view.current_page > 0:
                        view.current_page -= 1
                        view.update_components()
                        embed = view.create_embed()
                        await interaction.response.edit_message(embed=embed, view=view)

            class NextButton(Button):
                def __init__(self, disabled=False):
                    super().__init__(label="Next ▶", style=discord.ButtonStyle.secondary, disabled=disabled)

                async def callback(self, interaction: discord.Interaction):
                    view = self.view
                    if view.current_page < view.total_pages - 1:
                        view.current_page += 1
                        view.update_components()
                        embed = view.create_embed()
                        await interaction.response.edit_message(embed=embed, view=view)

            class CategorySelect(Select):
                def __init__(self, options):
                    super().__init__(
                        placeholder="Choose a category",
                        options=options
                    )

                async def callback(self, interaction: discord.Interaction):
                    try:
                        # Get items for selected category
                        with bank.get_db_connection() as conn:
                            c = conn.cursor()
                            c.execute('''SELECT id, name, price, description, required_role
                                        FROM shop_items
                                        WHERE category = ?
                                        ORDER BY price''', (self.values[0],))
                            items = c.fetchall()

                        if not items:
                            await interaction.response.send_message("No items found in this category!", ephemeral=True)
                            return

                        # Create paginated shop view
                        view = PaginatedShopView(items, self.values[0])
                        embed = view.create_embed()

                        await interaction.response.edit_message(embed=embed, view=view)
                    except Exception as e:
                        print(f"Error in category select callback: {e}")
                        error_embed = Embed(
                            title="❌ Error",
                            description="An error occurred while loading the items.",
                            color=bank.error_color
                        )
                        await interaction.response.edit_message(embed=error_embed, view=None)

            # Send initial category selection
            embed = Embed(
                title="🏪 Shop Categories",
                description="Select a category to browse:",
                color=bank.info_color
            )
            
            await interaction.followup.send(
                embed=embed,
                view=ShopView(),
                ephemeral=True
            )
    except Exception as e:
        print(f"Error in shop command: {e}")
        error_embed = Embed(
            title="❌ Shop Error",
            description="An error occurred while loading the shop.",
            color=bank.error_color
        )
        if not interaction.response.is_done():
            await interaction.response.send_message(embed=error_embed, ephemeral=True)
        else:
            await interaction.followup.send(embed=error_embed, ephemeral=True)

@bot.tree.command(name="balance", description="Check your balance or another user's balance")
@app_commands.guild_only()
async def balance(interaction: discord.Interaction, user: Optional[discord.Member] = None):
    # Immediately defer the response to prevent timeout
    is_private = True
    try:
        # If no user specified, show own balance
        if user is None:
            user = interaction.user
            # Viewing own balance should be private
            is_private = True
        # If trying to view someone else's balance, check permissions
        elif user != interaction.user:
            has_permission = False
            for role in interaction.user.roles:
                if role.id in bank.banker_roles:
                    has_permission = True
                    break
            
            if has_permission:
                # Banker checking someone else - make it public
                is_private = False
            else:
                # Non-banker trying to check others - private error
                await interaction.response.send_message(
                    embed=Embed(
                        title="❌ Access Denied",
                        description="You can only check your own balance!",
                        color=bank.error_color
                    ), 
                    ephemeral=True
                )
                return
        
        # Now defer with proper ephemeral setting
        await interaction.response.defer(ephemeral=is_private)
        
        # Create simplified balance embed
        try:
            user_id = str(user.id)
            # Update username in database
            bank.update_username(user_id, user.display_name)
            
            # Get balance with timeout protection
            balance_knuts = bank.get_balance(user_id)
            galleons, sickles, knuts = bank.convert_to_all_denominations(balance_knuts)
            
            embed = Embed(
                title=f"{bank.bank_emoji} Bank Statement",
                color=bank.info_color
            )
            embed.add_field(
                name=f"{bank.currency_emoji['galleon']} Galleons",
                value=f"**{galleons}**",
                inline=True
            )
            embed.add_field(
                name=f"{bank.currency_emoji['sickle']} Sickles",
                value=f"**{sickles}**",
                inline=True
            )
            embed.add_field(
                name=f"{bank.currency_emoji['knut']} Knuts",
                value=f"**{knuts}**",
                inline=True
            )
            
            if user == interaction.user:
                embed.set_footer(text=f"Account Holder: {user.display_name}")
            else:
                embed.set_footer(text=f"Account Holder: {user.display_name} | Viewed by: {interaction.user.display_name}")
            
            # Send response
            await interaction.followup.send(embed=embed, ephemeral=is_private)
        except Exception as query_error:
            print(f"Error in balance query: {query_error}")
            error_embed = Embed(
                title="❌ Error",
                description="An error occurred while fetching the balance information.",
                color=bank.error_color
            )
            try:
                await interaction.followup.send(embed=error_embed, ephemeral=True)
            except Exception as e:
                print(f"Failed to send query error message: {e}")
    except Exception as e:
        print(f"Error in balance command: {e}")
        try:
            error_embed = Embed(
                title="❌ Error",
                description="An error occurred while processing your balance request.",
                color=bank.error_color
            )
            if not interaction.response.is_done():
                await interaction.response.send_message(embed=error_embed, ephemeral=True)
            else:
                try:
                    await interaction.followup.send(embed=error_embed, ephemeral=True)
                except discord.NotFound:
                    # If interaction expired, we can't do anything more
                    print("Interaction expired before we could respond with error")
        except Exception as inner_e:
            print(f"Failed to send error message: {inner_e}")

@bot.tree.command(name="profile", description="View your or another user's profile")
@app_commands.guild_only()
@app_commands.describe(user="The user whose profile to view")
async def profile(interaction: discord.Interaction, user: Optional[discord.Member] = None):
    try:
        # Use deferred response to prevent timeout
        await interaction.response.defer()
        
        target_user = user or interaction.user
        user_id = str(target_user.id)
        
        # Get user's house role
        house_roles = {
            bank.config['house_roles']['gryffindor']: ("Gryffindor", 0xAE0001),
            bank.config['house_roles']['slytherin']: ("Slytherin", 0x2A623D),
            bank.config['house_roles']['ravenclaw']: ("Ravenclaw", 0x222F5B),
            bank.config['house_roles']['hufflepuff']: ("Hufflepuff", 0xFDB347)
        }
        
        user_house = "Unsorted"
        house_color = 0x808080  # Default gray
        house_emoji = "🏰"  # Default castle emoji for unsorted users
        
        # Check if target_user is a Member (has roles)
        if hasattr(target_user, 'roles'):
            for role in target_user.roles:
                if role.id in house_roles:
                    user_house, house_color = house_roles[role.id]
                    house_emoji = bank.house_emoji[user_house.lower()]
                    break
        else:
            logger.warning(f"User {user_id} ({target_user.display_name}) doesn't have roles attribute in profile command")
        
        # Check if user is VIP and get VIP details
        is_vip = False
        is_premium_vip = False
        vip_info = bank.get_vip_info(user_id)
        
        if vip_info:
            is_vip = True
            # Check if quarterly VIP for premium badge
            is_premium_vip = vip_info['tier'] == 'quarterly'
        
        with bank.get_db_connection() as conn:
            c = conn.cursor()
            
            # Get wand info
            c.execute('''
                SELECT COALESCE(s.name, r.name) as name, 
                       COALESCE(s.properties, r.properties) as properties
                FROM inventory i
                LEFT JOIN shop_items s ON i.item_id = s.id AND i.is_removed_item = 0
                LEFT JOIN removed_shop_items r ON i.item_id = r.id AND i.is_removed_item = 1
                WHERE i.user_id = ? AND COALESCE(s.category, r.category) = 'Wands'
            ''', (user_id,))
            wand = c.fetchone()
            
            # Get accessories (separate query)
            c.execute('''
                SELECT COALESCE(s.name, r.name) as name, 
                       COALESCE(s.properties, r.properties) as properties,
                       COALESCE(s.description, r.description) as description
                FROM inventory i
                LEFT JOIN shop_items s ON i.item_id = s.id AND i.is_removed_item = 0
                LEFT JOIN removed_shop_items r ON i.item_id = r.id AND i.is_removed_item = 1
                WHERE i.user_id = ? AND COALESCE(s.category, r.category) = 'Accessories'
                ORDER BY name
            ''', (user_id,))
            accessories = c.fetchall()
            
            # Get broom info
            c.execute('''
                SELECT COALESCE(s.name, r.name) as name, 
                       COALESCE(s.properties, r.properties) as properties
                FROM inventory i
                LEFT JOIN shop_items s ON i.item_id = s.id AND i.is_removed_item = 0
                LEFT JOIN removed_shop_items r ON i.item_id = r.id AND i.is_removed_item = 1
                WHERE i.user_id = ? AND COALESCE(s.category, r.category) = 'Brooms'
            ''', (user_id,))
            broom = c.fetchone()
            
            # Get other inventory items
            c.execute('''
                SELECT COALESCE(s.name, r.name) as name,
                       COALESCE(s.description, r.description) as description,
                       COALESCE(s.category, r.category) as category
                FROM inventory i
                LEFT JOIN shop_items s ON i.item_id = s.id AND i.is_removed_item = 0
                LEFT JOIN removed_shop_items r ON i.item_id = r.id AND i.is_removed_item = 1
                WHERE i.user_id = ? 
                AND COALESCE(s.category, r.category) NOT IN ('Wands', 'Brooms', 'Accessories')
                ORDER BY category, name
            ''', (user_id,))
            inventory_items = c.fetchall()
        
        # Use VIP color if VIP member
        profile_color = bank.vip_color if is_vip else house_color
        
        embed = Embed(
            title=f"{target_user.display_name}'s Profile",
            color=profile_color
        )
        
        # Add user info
        embed.set_thumbnail(url=target_user.display_avatar.url)
        
        # Create title with VIP badge if VIP
        title_parts = []
        title_parts.append(f"{house_emoji} {user_house}")
        
        if is_vip:
            # Use premium badge for quarterly VIP
            vip_badge = bank.premium_vip_emoji if is_premium_vip else bank.vip_emoji
            vip_title = "Premium VIP Member" if is_premium_vip else "VIP Member"
            title_parts.append(f"{vip_badge} {vip_title}")
            
            # Add VIP details
            if vip_info:
                embed.add_field(
                    name="VIP Status",
                    value=f"**Tier:** {vip_info['name']}\n"
                          f"**Expires in:** {vip_info['days_remaining']} days",
                    inline=True
                )
        
        embed.add_field(
            name="Status",
            value="\n".join(title_parts),
            inline=True
        )
        
        # Add wand info if they have one
        if wand:
            name, properties = wand
            if properties:
                wand_props = json.loads(properties)
                power = wand_props.get('power', 'Unknown')
                embed.add_field(
                    name="Wand",
                    value=f"**{name}** ({power} Power)\nWood: {wand_props['wood']} - Core: {wand_props['core']} - {wand_props['length']} inches - {wand_props['flexibility']}",
                    inline=False
                )
        
        # Add accessories if they have any
        if accessories:
            accessories_dict = {}  # To group duplicate accessories
            for name, properties, description in accessories:
                if properties:
                    props = json.loads(properties)
                    key = f"{name}|{properties}"  # Use name and properties as unique key
                    
                    if key in accessories_dict:
                        accessories_dict[key]['count'] += 1
                    else:
                        accessories_dict[key] = {
                            'name': name,
                            'props': props,
                            'description': description,
                            'count': 1
                        }
            
            if accessories_dict:
                accessories_text = []
                for acc_data in accessories_dict.values():
                    acc_text = f"**{acc_data['name']}"
                    if acc_data['count'] > 1:
                        acc_text += f" (x{acc_data['count']})"
                    acc_text += "**\n"
                    acc_text += f"Material: {acc_data['props']['material']}\n" \
                               f"Type: {acc_data['props']['type']}\n" \
                               f"Enchantment: {acc_data['props']['enchantment']}"
                    if acc_data['description']:
                        acc_text += f"\n{acc_data['description']}"
                    accessories_text.append(acc_text)
                
                embed.add_field(
                    name="Accessories",
                    value="\n\n".join(accessories_text),
                    inline=False
                )
        
        # Add broom info if they have one
        if broom:
            name, properties = broom
            if properties:
                broom_props = json.loads(properties)
                embed.add_field(
                    name="Broom",
                    value=f"{broom_props['length']} inches, {broom_props['wood']} handle\n"
                          f"{broom_props['bristle']}, {broom_props['speed']} speed",
                    inline=False
                )
        
        # Add other inventory items
        if inventory_items:
            current_category = None
            category_items = []
            
            for name, description, category in inventory_items:
                if category != current_category:
                    if category_items:
                        embed.add_field(
                            name=current_category,
                            value="\n".join(category_items),
                            inline=False
                        )
                    current_category = category
                    category_items = []
                item_text = f"• {name}"
                if description:
                    item_text += f" - {description}"
                category_items.append(item_text)
            
            if category_items:
                embed.add_field(
                    name=current_category,
                    value="\n".join(category_items),
                    inline=False
                )
        
        await interaction.followup.send(embed=embed)
    except Exception as e:
        print(f"Error in profile command: {e}")
        error_embed = Embed(
            title="❌ Error",
            description="An error occurred while processing your profile request.",
            color=bank.error_color
        )
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(embed=error_embed, ephemeral=True)
            else:
                await interaction.followup.send(embed=error_embed, ephemeral=True)
        except Exception as inner_e:
            print(f"Failed to send error message: {inner_e}")

@bot.tree.command(name="create_wand", description="Add a wand to the shop (Shop Managers only)")
@app_commands.guild_only()
@app_commands.describe(
    name="Name of the wand",
    price="Amount to set as price",
    currency="Currency type (Galleons, Sickles, or Knuts)",
    wood="Type of wood (e.g., Oak, Holly, Yew)",
    core="Wand core (e.g., Phoenix Feather, Dragon Heartstring)",
    length="Length in inches (e.g., 11.5)",
    flexibility="Wand flexibility (e.g., Quite Flexible, Rigid)",
    power="Wand power level (e.g., Strong, Average, Exceptional)",
    required_role="Role ID required to purchase this wand (optional)"
)
async def create_wand(
    interaction: discord.Interaction, 
    name: str,
    price: int,
    currency: Literal["Galleons", "Sickles", "Knuts"],
    wood: str,
    core: str,
    length: float,
    flexibility: str,
    power: str,
    required_role: Optional[str] = None
):
    # Use deferred response to prevent timeout
    await interaction.response.defer()
    
    # Check if user has shop manager role
    has_permission = False
    for role in interaction.user.roles:
        if role.id in bank.config['shop_manager_roles']:
            has_permission = True
            break
    
    if not has_permission and interaction.user.id != bot.owner_id:
        await interaction.followup.send(
            "You don't have permission to create wands!",
            ephemeral=True
        )
        return
    
    # Convert to knuts
    if currency == "Galleons":
        price_in_knuts = price * bank.KNUTS_PER_GALLEON
    elif currency == "Sickles":
        price_in_knuts = price * bank.KNUTS_PER_SICKLE
    else:
        price_in_knuts = price
    
    # Create wand properties
    properties = {
        "wood": wood,
        "core": core,
        "length": length,
        "flexibility": flexibility,
        "power": power
    }
    
    with bank.get_db_connection() as conn:
        c = conn.cursor()
        c.execute('''INSERT INTO shop_items 
                    (name, price, category, description, added_by, properties, required_role)
                    VALUES (?, ?, ?, ?, ?, ?, ?)''',
                 (name, 
                  price_in_knuts, 
                  "Wands",
                  f"{length} inches, {wood} with {core} core, {flexibility}, {power} power",
                  str(interaction.user.id),
                  json.dumps(properties),
                  required_role))
        conn.commit()
    
    embed = Embed(
        title="✨ New Wand Added",
        description=f"Added **{name}** to the shop",
        color=bank.success_color
    )
    embed.add_field(
        name="Price",
        value=f"**{price}** {currency}\n({bank.format_currency(price_in_knuts)})",
        inline=False
    )
    embed.add_field(
        name="Properties",
        value=f"Wood: {wood}\n"
              f"Core: {core}\n"
              f"Length: {length} inches\n"
              f"Flexibility: {flexibility}\n"
              f"Power: {power}",
        inline=False
    )
    
    await interaction.followup.send(embed=embed)

    # After successful wand creation, before sending response
    log_embed = Embed(
        title="🪄 New Wand Created",
        description=f"{interaction.user.mention} created a new wand",
        color=bank.info_color,
        timestamp=datetime.now()
    )
    log_embed.add_field(
        name="Wand Details",
        value=f"**{name}**\nPrice: {bank.format_currency(price_in_knuts)}",
        inline=False
    )
    log_embed.add_field(
        name="Properties",
        value=f"Wood: {wood}\n"
              f"Core: {core}\n"
              f"Length: {length} inches\n"
              f"Flexibility: {flexibility}\n"
              f"Power: {power}",
        inline=False
    )
    log_embed.set_footer(text=f"Created by: {interaction.user.id}")
    
    # Send to log channel
    await bank.log_to_channel(bot, log_embed)

    # Update the log embed to include required role
    log_embed.add_field(
        name="Required Role",
        value=f"<@&{required_role}>" if required_role else "None",
        inline=False
    )

@bot.tree.command(name="create_broom", description="Add a broom to the shop (Shop Managers only)")
@app_commands.guild_only()
@app_commands.describe(
    name="Name of the broom",
    price="Amount to set as price",
    currency="Currency type (Galleons, Sickles, or Knuts)",
    wood="Type of wood (e.g., Oak, Ash, Hazel)",
    bristle="Bristle material (e.g., Birch twigs, Hazel twigs)",
    length="Length in inches (e.g., 45.5)",
    speed="Speed rating (e.g., Fast, Average, Racing)",
    required_role="Role ID required to purchase this broom (optional)"
)
async def create_broom(
    interaction: discord.Interaction, 
    name: str,
    price: int,
    currency: Literal["Galleons", "Sickles", "Knuts"],
    wood: str,
    bristle: str,
    length: float,
    speed: str,
    required_role: Optional[str] = None
):
    # Use deferred response to prevent timeout
    await interaction.response.defer()
    
    # Check if user has shop manager role
    has_permission = False
    for role in interaction.user.roles:
        if role.id in bank.config['shop_manager_roles']:
            has_permission = True
            break
    
    if not has_permission and interaction.user.id != bot.owner_id:
        await interaction.followup.send(
            "You don't have permission to create brooms!",
            ephemeral=True
        )
        return
    
    # Convert to knuts
    if currency == "Galleons":
        price_in_knuts = price * bank.KNUTS_PER_GALLEON
    elif currency == "Sickles":
        price_in_knuts = price * bank.KNUTS_PER_SICKLE
    else:
        price_in_knuts = price
    
    # Create broom properties
    properties = {
        "wood": wood,
        "bristle": bristle,
        "length": length,
        "speed": speed
    }
    
    with bank.get_db_connection() as conn:
        c = conn.cursor()
        c.execute('''INSERT INTO shop_items 
                    (name, price, category, description, added_by, properties, required_role)
                    VALUES (?, ?, ?, ?, ?, ?, ?)''',
                 (name, 
                  price_in_knuts, 
                  "Brooms",
                  f"{length} inches, {wood} handle with {bristle}, {speed} speed",
                  str(interaction.user.id),
                  json.dumps(properties),
                  required_role))
        conn.commit()
    
    embed = Embed(
        title="✨ New Broom Added",
        description=f"Added **{name}** to the shop",
        color=bank.success_color
    )
    embed.add_field(
        name="Price",
        value=f"**{price}** {currency}\n({bank.format_currency(price_in_knuts)})",
        inline=False
    )
    embed.add_field(
        name="Properties",
        value=f"Wood: {wood}\n"
              f"Bristle: {bristle}\n"
              f"Length: {length} inches\n"
              f"Speed: {speed}",
        inline=False
    )
    
    await interaction.followup.send(embed=embed)

    # After successful broom creation, before sending response
    log_embed = Embed(
        title="🧹 New Broom Created",
        description=f"{interaction.user.mention} created a new broom",
        color=bank.info_color,
        timestamp=datetime.now()
    )
    log_embed.add_field(
        name="Broom Details",
        value=f"**{name}**\nPrice: {bank.format_currency(price_in_knuts)}",
        inline=False
    )
    log_embed.add_field(
        name="Properties",
        value=f"Wood: {wood}\n"
              f"Bristle: {bristle}\n"
              f"Length: {length} inches\n"
              f"Speed: {speed}",
        inline=False
    )
    log_embed.set_footer(text=f"Created by: {interaction.user.id}")
    
    # Send to log channel
    await bank.log_to_channel(bot, log_embed)

    # Update the log embed to include required role
    log_embed.add_field(
        name="Required Role",
        value=f"<@&{required_role}>" if required_role else "None",
        inline=False
    )

@bot.tree.command(name="destroy", description="Destroy your wand, broom, or accessories")
@app_commands.guild_only()
@app_commands.describe(
    item_type="Choose which type of item to destroy"
)
async def destroy(
    interaction: discord.Interaction,
    item_type: Literal["Wand", "Broom", "Accessories"]
):
    # Use deferred response to prevent timeout
    await interaction.response.defer(ephemeral=True)
    
    user_id = str(interaction.user.id)
    # Fix category name handling
    category = "Accessories" if item_type == "Accessories" else f"{item_type}s"
    
    # Get item details with modified query for accessories
    with bank.get_db_connection() as conn:
        c = conn.cursor()
        
        # Get the specific items we want, checking both active and removed items
        c.execute('''
            SELECT 
                COALESCE(s.name, r.name) as name,
                COALESCE(s.properties, r.properties) as properties,
                i.id,
                COALESCE(s.category, r.category) as category
            FROM inventory i
            LEFT JOIN shop_items s ON i.item_id = s.id AND i.is_removed_item = 0
            LEFT JOIN removed_shop_items r ON i.item_id = r.id AND i.is_removed_item = 1
            WHERE i.user_id = ? 
            AND LOWER(COALESCE(s.category, r.category)) = LOWER(?)
        ''', (user_id, category))
        
        items = c.fetchall()
    
    if not items:
        await interaction.followup.send(
            f"You don't have any {item_type.lower()} to destroy! (Category: {category})",
            ephemeral=True
        )
        return

    # If it's a wand or broom (single item), proceed directly to confirmation
    if item_type in ["Wand", "Broom"]:
        name, properties, inventory_id, _ = items[0]
        
        # Create confirmation embed
        if properties:
            props = json.loads(properties)
            if category == "Wands":
                details = f"{props['length']} inches, {props['wood']}\n" \
                         f"{props['core']} core, {props['flexibility']}\n" \
                         f"Power: {props.get('power', 'Unknown')}"
            else:  # Brooms
                details = f"{props['length']} inches, {props['wood']} handle\n" \
                         f"{props['bristle']}, {props['speed']} speed"
        else:
            details = name
        
        embed = Embed(
            title=f"🔥 Destroy {item_type}?",
            description=f"Are you sure you want to destroy your {item_type.lower()}?\n\n" \
                       f"**{name}**\n" \
                       f"*{details}*",
            color=bank.error_color
        )
        
        # Create confirmation buttons
        class ConfirmButtons(View):
            def __init__(self, inventory_id: int):
                super().__init__(timeout=60)
                self.inventory_id = inventory_id
            
            @discord.ui.button(label="Destroy", style=discord.ButtonStyle.danger)
            async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
                with bank.get_db_connection() as conn:
                    c = conn.cursor()
                    c.execute('DELETE FROM inventory WHERE id = ?', (self.inventory_id,))
                    conn.commit()
                
                result_embed = Embed(
                    title=f"💥 {item_type} Destroyed",
                    description=f"Your {item_type.lower()} has been destroyed.",
                    color=bank.error_color
                )
                await interaction.response.edit_message(embed=result_embed, view=None)
            
            @discord.ui.button(label="Cancel", style=discord.ButtonStyle.secondary)
            async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
                cancel_embed = Embed(
                    title="❌ Cancelled",
                    description=f"Your {item_type.lower()} is safe.",
                    color=bank.info_color
                )
                await interaction.response.edit_message(embed=cancel_embed, view=None)
        
        await interaction.followup.send(
            embed=embed,
            view=ConfirmButtons(inventory_id),
            ephemeral=True
        )
    
    else:  # For accessories, show selection menu first
        # Create paginated accessory destroy view
        view = PaginatedDestroyAccessoryView(items)
        embed = view.create_embed()

        await interaction.followup.send(
            embed=embed,
            view=view,
            ephemeral=True
        )

class PaginatedDestroyAccessoryView(View):
    def __init__(self, items):
        super().__init__(timeout=60)
        self.items = items
        self.current_page = 0
        self.items_per_page = 25
        self.total_pages = (len(items) - 1) // self.items_per_page + 1

        self.update_components()

    def get_current_items(self):
        start = self.current_page * self.items_per_page
        end = start + self.items_per_page
        return self.items[start:end]

    def create_embed(self):
        embed = Embed(
            title="🔥 Destroy Accessory",
            description="Select an accessory to destroy:",
            color=bank.error_color
        )

        if self.total_pages > 1:
            embed.add_field(
                name="📄 Page Info",
                value=f"Page {self.current_page + 1} of {self.total_pages} ({len(self.items)} total accessories)",
                inline=False
            )

        return embed

    def update_components(self):
        self.clear_items()

        # Add accessory select
        current_items = self.get_current_items()
        if current_items:
            self.add_item(DestroyAccessorySelect(current_items, self.items))

        # Add pagination buttons if needed
        if self.total_pages > 1:
            self.add_item(DestroyPreviousButton(disabled=self.current_page == 0))
            self.add_item(DestroyNextButton(disabled=self.current_page == self.total_pages - 1))

class DestroyAccessorySelect(Select):
    def __init__(self, current_items, all_items):
        self.all_items = all_items
        options = []
        for name, properties, inventory_id, _ in current_items:
            description = ""
            if properties:
                props = json.loads(properties)
                description = f"{props['material']} {props['type']}, {props['enchantment']}"

            options.append(
                SelectOption(
                    label=name[:100],  # Ensure label fits Discord limit
                    value=str(inventory_id),
                    description=description[:100]  # Discord limit
                )
            )

        super().__init__(
            placeholder="Choose an accessory to destroy",
            options=options,
            min_values=1,
            max_values=1
        )

    async def callback(self, interaction: discord.Interaction):
        selected_id = int(self.values[0])
        selected_item = next(item for item in self.all_items if item[2] == selected_id)
        name, properties, inventory_id, _ = selected_item

        # Create confirmation embed
        if properties:
            props = json.loads(properties)
            details = f"Material: {props['material']}\n" \
                     f"Type: {props['type']}\n" \
                     f"Enchantment: {props['enchantment']}"
        else:
            details = name

        embed = Embed(
            title="🔥 Destroy Accessory?",
            description=f"Are you sure you want to destroy this accessory?\n\n" \
                       f"**{name}**\n" \
                       f"*{details}*",
            color=bank.error_color
        )

        # Create confirmation buttons
        class ConfirmButtons(View):
            def __init__(self, inventory_id: int):
                super().__init__(timeout=60)
                self.inventory_id = inventory_id

            @discord.ui.button(label="Destroy", style=discord.ButtonStyle.danger)
            async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
                with bank.get_db_connection() as conn:
                    c = conn.cursor()
                    c.execute('DELETE FROM inventory WHERE id = ?', (self.inventory_id,))
                    conn.commit()

                result_embed = Embed(
                    title="💥 Accessory Destroyed",
                    description=f"Your accessory has been destroyed.",
                    color=bank.error_color
                )
                await interaction.response.edit_message(embed=result_embed, view=None)

            @discord.ui.button(label="Cancel", style=discord.ButtonStyle.secondary)
            async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
                cancel_embed = Embed(
                    title="❌ Cancelled",
                    description="Your accessory is safe.",
                    color=bank.info_color
                )
                await interaction.response.edit_message(embed=cancel_embed, view=None)

        await interaction.response.edit_message(
            embed=embed,
            view=ConfirmButtons(inventory_id)
        )

class DestroyPreviousButton(Button):
    def __init__(self, disabled=False):
        super().__init__(label="◀ Previous", style=discord.ButtonStyle.secondary, disabled=disabled)

    async def callback(self, interaction: discord.Interaction):
        view = self.view
        if view.current_page > 0:
            view.current_page -= 1
            view.update_components()
            embed = view.create_embed()
            await interaction.response.edit_message(embed=embed, view=view)

class DestroyNextButton(Button):
    def __init__(self, disabled=False):
        super().__init__(label="Next ▶", style=discord.ButtonStyle.secondary, disabled=disabled)

    async def callback(self, interaction: discord.Interaction):
        view = self.view
        if view.current_page < view.total_pages - 1:
            view.current_page += 1
            view.update_components()
            embed = view.create_embed()
            await interaction.response.edit_message(embed=embed, view=view)

# Load config
with open('config.json') as f:
    config = json.load(f)

# Database backup tasks
@tasks.loop(hours=24)
async def backup_database_midnight():
    # Wait until next midnight for first execution
    now = datetime.now()
    if now.hour != 0 or now.minute != 0:
        # Wait until next midnight
        next_run = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
        seconds_until_midnight = (next_run - now).total_seconds()
        await asyncio.sleep(seconds_until_midnight)
    
    # Run backups at midnight (12am)
    perform_database_backup("midnight")
    
@tasks.loop(hours=24)
async def backup_database_noon():
    # This will run every 24 hours, starting after the bot has been running for the initial wait time
    now = datetime.now()
    if now.hour != 12 or now.minute != 0:
        next_run = now.replace(hour=12, minute=0, second=0, microsecond=0)
        if now.hour >= 12:
            next_run += timedelta(days=1)
        seconds_until_noon = (next_run - now).total_seconds()
        print(f"Noon backup scheduled in {seconds_until_noon/3600:.2f} hours")
        await asyncio.sleep(seconds_until_noon)
    
    print("Running noon backup now")
    perform_database_backup("noon")

def perform_database_backup(time_label):
    try:
        print(f"Starting {time_label} backup process")
        
        # Create backup directory if it doesn't exist
        backup_dir = "database_backups"
        os.makedirs(backup_dir, exist_ok=True)
        print(f"Backup directory exists: {os.path.exists(backup_dir)}")
        
        # Get the full path of the current directory for debugging
        current_dir = os.getcwd()
        print(f"Current working directory: {current_dir}")
        
        # Generate backup filename with timestamp
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        backup_filename = f"{backup_dir}/bank_backup_{time_label}_{timestamp}.db"
        print(f"Will create backup file: {backup_filename}")
        
        # Check if source DB exists
        db_path = "bank.db"
        if not os.path.exists(db_path):
            print(f"ERROR: Source database not found at {os.path.abspath(db_path)}")
            return
            
        # Create a backup of the database
        with bank.get_db_connection() as conn:
            # Ensure all changes are written to disk before backing up
            conn.execute("PRAGMA wal_checkpoint(FULL)")
        
        # Copy the database file
        shutil.copy2(db_path, backup_filename)
        
        # Log backup success
        print(f"Database backup created: {backup_filename}")
        
        # Remove old backups (keep last 7 days of each type)
        cleanup_old_backups(time_label)
        
    except Exception as e:
        print(f"Error creating database backup: {e}")
        import traceback
        traceback.print_exc()

def cleanup_old_backups(time_label):
    try:
        # Ensure backup directory exists
        if not os.path.exists("database_backups"):
            return
            
        # Get all backup files of the specified type
        backup_files = [f for f in os.listdir("database_backups") 
                       if f.startswith(f"bank_backup_{time_label}_") and f.endswith(".db")]
        
        # Sort by creation time (oldest first)
        backup_files.sort(key=lambda x: os.path.getctime(os.path.join("database_backups", x)))
        
        # Remove all but the 7 most recent backups
        if len(backup_files) > 7:
            for old_file in backup_files[:-7]:
                file_path = os.path.join("database_backups", old_file)
                os.remove(file_path)
                print(f"Removed old backup: {file_path}")
                
    except Exception as e:
        print(f"Error cleaning up old backups: {e}")

# Add command to manually trigger backup
@bot.tree.command(name="backup_db", description="Manually create a database backup (Owner only)")
@app_commands.guild_only()
async def backup_db(interaction: discord.Interaction):
    if interaction.user.id != bot.owner_id:
        embed = Embed(
            title="❌ Access Denied",
            description="Only the bot owner can use this command!",
            color=bank.error_color
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return
        
    try:
        # Create a manual backup
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        backup_filename = f"database_backups/bank_backup_manual_{timestamp}.db"
        
        # Ensure directory exists
        os.makedirs("database_backups", exist_ok=True)
        
        # Checkpoint the WAL file to make sure all changes are in the main db file
        with bank.get_db_connection() as conn:
            conn.execute("PRAGMA wal_checkpoint(FULL)")
            
        # Copy the database
        shutil.copy2("bank.db", backup_filename)
        
        embed = Embed(
            title="✅ Backup Successful",
            description=f"Database backed up to: `{backup_filename}`",
            color=bank.success_color
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        
    except Exception as e:
        embed = Embed(
            title="❌ Backup Failed",
            description=f"Error: {str(e)}",
            color=bank.error_color
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="modify_balance", description="Modify a user's balance (Bank Masters only)")
@app_commands.guild_only()
@app_commands.describe(
    user="The user whose balance to modify",
    amount="Amount to add/remove",
    currency="Currency type (Galleons, Sickles, or Knuts)"
)
async def modify_balance(interaction: discord.Interaction, user: discord.Member, amount: int, currency: Literal["Galleons", "Sickles", "Knuts"]):
    # Use deferred response to prevent timeouts
    await defer_response(interaction)
    
    has_permission = False
    for role in interaction.user.roles:
        if role.id in bank.banker_roles:
            has_permission = True
            break
    
    if not has_permission and interaction.user.id != bot.owner_id:
        embed = Embed(
            title="❌ Access Denied",
            description="You don't have permission to modify balances!",
            color=bank.error_color
        )
        await send_message(interaction, embed=embed, ephemeral=True)
        return
    
    user_id = str(user.id)
    username = user.display_name
    
    # Convert to knuts
    if currency == "Galleons":
        knuts_amount = amount * bank.KNUTS_PER_GALLEON
    elif currency == "Sickles":
        knuts_amount = amount * bank.KNUTS_PER_SICKLE
    else:
        knuts_amount = amount
    
    # Get old balance for logging
    old_balance = bank.get_balance(user_id)
    
    # Update balance
    bank.update_balance(user_id, knuts_amount, username)
    
    # Log admin modification
    bank.log_transaction(
        user_id,
        knuts_amount,
        'admin',
        str(interaction.user.id),
        f"Admin balance modification: {amount} {currency} by {interaction.user.display_name}"
    )
    
    # Get new balance
    new_balance = bank.get_balance(user_id)
    
    embed = Embed(
        title="💰 Balance Modified",
        description=f"Modified {user.mention}'s balance",
        color=bank.success_color
    )
    
    # Display appropriate wording and sign based on whether amount is positive or negative
    if amount >= 0:
        action_word = "Added"
        display_amount = amount
        amount_str = f"**{action_word}:** +{display_amount} {currency}\n({bank.format_currency(knuts_amount)})"
    else:
        action_word = "Removed"
        display_amount = abs(amount)
        amount_str = f"**{action_word}:** -{display_amount} {currency}\n({bank.format_currency(abs(knuts_amount))})"
    
    embed.add_field(
        name="Modification",
        value=amount_str,
        inline=False
    )
    
    embed.add_field(
        name="Old Balance",
        value=bank.format_currency(old_balance),
        inline=False
    )
    
    embed.add_field(
        name="New Balance",
        value=bank.format_currency(new_balance),
        inline=False
    )
    
    # Create log embed
    log_embed = Embed(
        title="Balance Modification Log",
        description=f"Balance modified by {interaction.user.mention}",
        color=bank.info_color,
        timestamp=datetime.now()
    )
    log_embed.add_field(
        name="Target User",
        value=f"{user.mention} ({user.id})",
        inline=False
    )
    
    # Format amount display for the log embed too
    if knuts_amount >= 0:
        log_amount_str = f"+{bank.format_currency(knuts_amount)}"
    else:
        log_amount_str = f"-{bank.format_currency(abs(knuts_amount))}"
        
    log_embed.add_field(
        name="Modification",
        value=f"Amount: {log_amount_str}",
        inline=False
    )
    log_embed.add_field(
        name="New Balance",
        value=bank.format_currency(new_balance),
        inline=False
    )
    
    # Send to log channel
    await bank.log_to_channel(bot, log_embed)
    
    # Send original response using our helper function
    await send_message(interaction, embed=embed)

@bot.tree.command(name="add_item", description="Add an item to the shop (Shop Managers only)")
@app_commands.guild_only()
async def add_item(interaction: discord.Interaction, 
                  name: str, 
                  price: int, 
                  currency: Literal["Galleons", "Sickles", "Knuts"],
                  category: str,
                  description: str,
                  required_role: Optional[str] = None):
    try:
        # Use deferred response to prevent timeout
        await defer_response(interaction)
        
        # Check if user has shop manager role
        has_permission = False
        for role in interaction.user.roles:
            if role.id in bank.config['shop_manager_roles']:
                has_permission = True
                break
        
        if not has_permission and interaction.user.id != bot.owner_id:
            await send_message(interaction,
                content="You don't have permission to add items to the shop!",
                ephemeral=True
            )
            return
        
        # Check if trying to add to default categories
        default_categories = ["Wands", "Brooms", "Accessories"]
        if category in default_categories:
            specific_command = ""
            if category == "Wands":
                specific_command = "/create_wand"
            elif category == "Brooms":
                specific_command = "/create_broom"
            else:  # Accessories
                specific_command = "/craft_accessories"
                
            await send_message(interaction,
                content=f"Please use the specific command for adding to the {category} category:\n{specific_command}",
                ephemeral=True
            )
            return
        
        # Convert to knuts
        if currency == "Galleons":
            price_in_knuts = price * bank.KNUTS_PER_GALLEON
        elif currency == "Sickles":
            price_in_knuts = price * bank.KNUTS_PER_SICKLE
        else:
            price_in_knuts = price
        
        # Add item to shop
        with bank.get_db_connection() as conn:
            c = conn.cursor()
            c.execute('''INSERT INTO shop_items 
                        (name, price, category, description, properties, added_by, required_role)
                        VALUES (?, ?, ?, ?, ?, ?, ?)''',
                    (name, 
                    price_in_knuts, 
                    category,
                    description,
                    json.dumps({}),  # Empty properties for general items
                    str(interaction.user.id),
                    required_role))
            conn.commit()
        
        # Create success embed
        embed = Embed(
            title="✅ Item Added",
            description=f"Added **{name}** to the shop in category **{category}**",
            color=bank.success_color
        )
        embed.add_field(
            name="Price",
            value=f"**{price}** {currency}\n({bank.format_currency(price_in_knuts)})",
            inline=False
        )
        if description:
            embed.add_field(
                name="Description",
                value=description,
                inline=False
            )
        if required_role:
            embed.add_field(
                name="Required Role",
                value=f"<@&{required_role}>",
                inline=False
            )
        
        # Create log embed
        log_embed = Embed(
            title="🛍️ New Shop Item Added",
            description=f"{interaction.user.mention} added a new item to the shop",
            color=bank.info_color,
            timestamp=datetime.now()
        )
        log_embed.add_field(
            name="Item Details",
            value=f"**{name}**\nCategory: {category}\nPrice: {bank.format_currency(price_in_knuts)}",
            inline=False
        )
        if description:
            log_embed.add_field(
                name="Description",
                value=description,
                inline=False
            )
        if required_role:
            log_embed.add_field(
                name="Required Role",
                value=f"<@&{required_role}>",
                inline=False
            )
        log_embed.set_footer(text=f"Added by: {interaction.user.id}")
        
        # Send to log channel
        await bank.log_to_channel(bot, log_embed)
        
        # Send response
        await send_message(interaction, embed=embed)
        
    except Exception as e:
        print(f"Error adding item to shop: {e}")
        error_embed = Embed(
            title="❌ Error",
            description=f"An error occurred while adding the item: {str(e)}",
            color=bank.error_color
        )
        await send_message(interaction, embed=error_embed, ephemeral=True)

@add_item.autocomplete('category')
async def category_autocomplete(interaction: discord.Interaction, current: str):
    try:
        # Get existing categories from database, excluding default ones
        default_categories = ["Wands", "Brooms", "Accessories"]

        with bank.get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT DISTINCT category FROM shop_items')
            categories = [row[0] for row in c.fetchall() if row[0] not in default_categories]

            # Add some suggested categories if they don't exist
            suggested = ["Potions", "Books", "Clothing", "Pets", "Food", "Magical Artifacts", "Scrolls", "Talismans"]
            categories.extend([s for s in suggested if s not in categories])

            # Remove duplicates and sort
            categories = sorted(set(categories))

        # Filter by current input and limit to 25 choices
        return [
            app_commands.Choice(name=cat, value=cat)
            for cat in categories
            if current.lower() in cat.lower()
        ][:25]

    except Exception as e:
        print(f"Error in category autocomplete: {e}")
        return [app_commands.Choice(name="Error loading categories", value="Error")]

@bot.tree.command(name="edit_item", description="Edit any item in the shop (Shop Managers only)")
@app_commands.guild_only()
@app_commands.describe(
    item_name="Name of the item to edit (use autocomplete)"
)
async def edit_item(interaction: discord.Interaction, item_name: str):
    try:
        # Use deferred response to prevent timeout
        await interaction.response.defer(ephemeral=True)

        # Check if user has shop manager role
        has_permission = False
        for role in interaction.user.roles:
            if role.id in bank.config['shop_manager_roles']:
                has_permission = True
                break

        if not has_permission and interaction.user.id != bot.owner_id:
            await interaction.followup.send(
                "You don't have permission to edit shop items!",
                ephemeral=True
            )
            return

        # Find the item in the database
        with bank.get_db_connection() as conn:
            c = conn.cursor()
            c.execute('''SELECT id, name, price, category, description, properties, required_role, added_by
                        FROM shop_items
                        WHERE name = ? COLLATE NOCASE''', (item_name,))
            item = c.fetchone()

            if not item:
                await interaction.followup.send(
                    f"Item '{item_name}' not found in the shop!",
                    ephemeral=True
                )
                return

            item_id, name, price, category, description, properties, required_role, added_by = item

            # Parse properties if they exist
            props = {}
            if properties:
                try:
                    props = json.loads(properties)
                except:
                    props = {}

        # Create the edit interface
        view = EditItemView(item_id, name, price, category, description, props, required_role, added_by)
        embed = view.create_embed()

        await interaction.followup.send(embed=embed, view=view, ephemeral=True)

    except Exception as e:
        print(f"Error in edit_item command: {e}")
        await interaction.followup.send(
            "An error occurred while trying to edit the item.",
            ephemeral=True
        )

class EditItemView(View):
    def __init__(self, item_id, name, price, category, description, properties, required_role, added_by):
        super().__init__(timeout=300)  # 5 minute timeout for editing
        self.item_id = item_id
        self.name = name
        self.price = price
        self.category = category
        self.description = description
        self.properties = properties
        self.required_role = required_role
        self.added_by = added_by

        # Add buttons for different edit options
        self.add_item(EditNameButton())
        self.add_item(EditPriceButton())
        self.add_item(EditCategoryButton())
        self.add_item(EditDescriptionButton())
        self.add_item(EditRequiredRoleButton())

        # Add properties button only for items that have properties
        if self.category in ["Wands", "Brooms", "Accessories"]:
            self.add_item(EditPropertiesButton())

        self.add_item(SaveChangesButton())
        self.add_item(CancelEditButton())

    def create_embed(self):
        embed = Embed(
            title=f"✏️ Editing: {self.name}",
            description="Click the buttons below to edit different aspects of this item.",
            color=bank.info_color
        )

        # Convert price back to readable format
        galleons = self.price // bank.KNUTS_PER_GALLEON
        remaining = self.price % bank.KNUTS_PER_GALLEON
        sickles = remaining // bank.KNUTS_PER_SICKLE
        knuts = remaining % bank.KNUTS_PER_SICKLE

        price_display = []
        if galleons > 0:
            price_display.append(f"{galleons} Galleons")
        if sickles > 0:
            price_display.append(f"{sickles} Sickles")
        if knuts > 0:
            price_display.append(f"{knuts} Knuts")
        price_text = " ".join(price_display) or "0 Knuts"

        embed.add_field(name="📝 Name", value=self.name, inline=True)
        embed.add_field(name="💰 Price", value=f"{price_text}\n({bank.format_currency(self.price)})", inline=True)
        embed.add_field(name="📂 Category", value=self.category, inline=True)
        embed.add_field(name="📄 Description", value=self.description or "None", inline=False)

        if self.required_role:
            embed.add_field(name="🔒 Required Role", value=f"<@&{self.required_role}>", inline=True)
        else:
            embed.add_field(name="🔒 Required Role", value="None", inline=True)

        # Show properties if they exist
        if self.properties:
            props_text = []
            for key, value in self.properties.items():
                props_text.append(f"**{key.title()}:** {value}")
            embed.add_field(name="⚙️ Properties", value="\n".join(props_text), inline=False)

        embed.add_field(name="👤 Added By", value=f"<@{self.added_by}>", inline=True)
        embed.set_footer(text=f"Item ID: {self.item_id}")

        return embed

class EditNameButton(Button):
    def __init__(self):
        super().__init__(label="📝 Edit Name", style=discord.ButtonStyle.secondary)

    async def callback(self, interaction: discord.Interaction):
        modal = EditNameModal(self.view)
        await interaction.response.send_modal(modal)

class EditPriceButton(Button):
    def __init__(self):
        super().__init__(label="💰 Edit Price", style=discord.ButtonStyle.secondary)

    async def callback(self, interaction: discord.Interaction):
        modal = EditPriceModal(self.view)
        await interaction.response.send_modal(modal)

class EditCategoryButton(Button):
    def __init__(self):
        super().__init__(label="📂 Edit Category", style=discord.ButtonStyle.secondary)

    async def callback(self, interaction: discord.Interaction):
        modal = EditCategoryModal(self.view)
        await interaction.response.send_modal(modal)

class EditDescriptionButton(Button):
    def __init__(self):
        super().__init__(label="📄 Edit Description", style=discord.ButtonStyle.secondary)

    async def callback(self, interaction: discord.Interaction):
        modal = EditDescriptionModal(self.view)
        await interaction.response.send_modal(modal)

class EditRequiredRoleButton(Button):
    def __init__(self):
        super().__init__(label="🔒 Edit Role", style=discord.ButtonStyle.secondary)

    async def callback(self, interaction: discord.Interaction):
        modal = EditRequiredRoleModal(self.view)
        await interaction.response.send_modal(modal)

class EditPropertiesButton(Button):
    def __init__(self):
        super().__init__(label="⚙️ Edit Properties", style=discord.ButtonStyle.secondary)

    async def callback(self, interaction: discord.Interaction):
        modal = EditPropertiesModal(self.view)
        await interaction.response.send_modal(modal)

class SaveChangesButton(Button):
    def __init__(self):
        super().__init__(label="✅ Save Changes", style=discord.ButtonStyle.green)

    async def callback(self, interaction: discord.Interaction):
        try:
            # Update the item in the database
            with bank.get_db_connection() as conn:
                c = conn.cursor()
                c.execute('''UPDATE shop_items
                            SET name = ?, price = ?, category = ?, description = ?,
                                properties = ?, required_role = ?
                            WHERE id = ?''',
                         (self.view.name, self.view.price, self.view.category,
                          self.view.description, json.dumps(self.view.properties),
                          self.view.required_role, self.view.item_id))
                conn.commit()

            # Create success embed
            embed = Embed(
                title="✅ Item Updated Successfully!",
                description=f"**{self.view.name}** has been updated in the shop.",
                color=bank.success_color
            )

            # Create log embed
            log_embed = Embed(
                title="✏️ Shop Item Edited",
                description=f"{interaction.user.mention} edited a shop item",
                color=bank.info_color,
                timestamp=datetime.now()
            )
            log_embed.add_field(
                name="Item Details",
                value=f"**{self.view.name}** (ID: {self.view.item_id})\nCategory: {self.view.category}",
                inline=False
            )
            log_embed.set_footer(text=f"Edited by: {interaction.user.id}")

            # Send to log channel
            await bank.log_to_channel(bot, log_embed)

            await interaction.response.edit_message(embed=embed, view=None)

        except Exception as e:
            print(f"Error saving item changes: {e}")
            await interaction.response.send_message(
                "An error occurred while saving changes.",
                ephemeral=True
            )

class CancelEditButton(Button):
    def __init__(self):
        super().__init__(label="❌ Cancel", style=discord.ButtonStyle.red)

    async def callback(self, interaction: discord.Interaction):
        embed = Embed(
            title="❌ Edit Cancelled",
            description="No changes were made to the item.",
            color=bank.error_color
        )
        await interaction.response.edit_message(embed=embed, view=None)

class EditNameModal(Modal):
    def __init__(self, view):
        super().__init__(title="Edit Item Name")
        self.view = view

        self.name_input = TextInput(
            label="Item Name",
            placeholder="Enter the new name for this item",
            default=view.name,
            max_length=100
        )
        self.add_item(self.name_input)

    async def on_submit(self, interaction: discord.Interaction):
        self.view.name = self.name_input.value
        embed = self.view.create_embed()
        await interaction.response.edit_message(embed=embed, view=self.view)

class EditPriceModal(Modal):
    def __init__(self, view):
        super().__init__(title="Edit Item Price")
        self.view = view

        # Convert current price to readable format
        galleons = view.price // bank.KNUTS_PER_GALLEON
        remaining = view.price % bank.KNUTS_PER_GALLEON
        sickles = remaining // bank.KNUTS_PER_SICKLE
        knuts = remaining % bank.KNUTS_PER_SICKLE

        self.price_input = TextInput(
            label="Price (format: 10G 5S 25K or just 1000)",
            placeholder="Examples: 10G 5S 25K, 1000K, 5G, 50S",
            default=f"{galleons}G {sickles}S {knuts}K" if galleons or sickles or knuts else "0K",
            max_length=50
        )
        self.add_item(self.price_input)

    async def on_submit(self, interaction: discord.Interaction):
        try:
            # Parse the price input
            price_str = self.price_input.value.strip().upper()
            total_knuts = 0

            # Handle different formats
            if 'G' in price_str or 'S' in price_str or 'K' in price_str:
                # Parse G/S/K format
                import re
                galleons_match = re.search(r'(\d+)G', price_str)
                sickles_match = re.search(r'(\d+)S', price_str)
                knuts_match = re.search(r'(\d+)K', price_str)

                if galleons_match:
                    total_knuts += int(galleons_match.group(1)) * bank.KNUTS_PER_GALLEON
                if sickles_match:
                    total_knuts += int(sickles_match.group(1)) * bank.KNUTS_PER_SICKLE
                if knuts_match:
                    total_knuts += int(knuts_match.group(1))
            else:
                # Assume it's just a number in knuts
                total_knuts = int(price_str)

            if total_knuts < 0:
                raise ValueError("Price cannot be negative")

            self.view.price = total_knuts
            embed = self.view.create_embed()
            await interaction.response.edit_message(embed=embed, view=self.view)

        except ValueError as e:
            await interaction.response.send_message(
                f"Invalid price format! Please use formats like: 10G 5S 25K, 1000K, 5G, or 50S",
                ephemeral=True
            )

class EditCategoryModal(Modal):
    def __init__(self, view):
        super().__init__(title="Edit Item Category")
        self.view = view

        self.category_input = TextInput(
            label="Category",
            placeholder="Enter the category (e.g., Wands, Brooms, Potions)",
            default=view.category,
            max_length=50
        )
        self.add_item(self.category_input)

    async def on_submit(self, interaction: discord.Interaction):
        self.view.category = self.category_input.value
        embed = self.view.create_embed()
        await interaction.response.edit_message(embed=embed, view=self.view)

class EditDescriptionModal(Modal):
    def __init__(self, view):
        super().__init__(title="Edit Item Description")
        self.view = view

        self.description_input = TextInput(
            label="Description",
            placeholder="Enter the item description",
            default=view.description or "",
            max_length=1000,
            style=discord.TextStyle.paragraph
        )
        self.add_item(self.description_input)

    async def on_submit(self, interaction: discord.Interaction):
        self.view.description = self.description_input.value if self.description_input.value.strip() else None
        embed = self.view.create_embed()
        await interaction.response.edit_message(embed=embed, view=self.view)

class EditRequiredRoleModal(Modal):
    def __init__(self, view):
        super().__init__(title="Edit Required Role")
        self.view = view

        self.role_input = TextInput(
            label="Required Role (ID, mention, or name)",
            placeholder="Enter role ID, @role, or role name. Leave empty for no requirement.",
            default=view.required_role or "",
            max_length=100,
            required=False
        )
        self.add_item(self.role_input)

    async def on_submit(self, interaction: discord.Interaction):
        role_input = self.role_input.value.strip()
        if role_input:
            try:
                # Parse the role input using the existing helper function
                role_id = bank.parse_role_input(role_input, interaction.guild)
                self.view.required_role = role_id
            except:
                await interaction.response.send_message(
                    "Invalid role! Please provide a valid role ID, mention, or name.",
                    ephemeral=True
                )
                return
        else:
            self.view.required_role = None

        embed = self.view.create_embed()
        await interaction.response.edit_message(embed=embed, view=self.view)

class EditPropertiesModal(Modal):
    def __init__(self, view):
        super().__init__(title="Edit Item Properties")
        self.view = view

        # Create a text representation of current properties
        props_text = ""
        if view.properties:
            for key, value in view.properties.items():
                props_text += f"{key}: {value}\n"

        self.properties_input = TextInput(
            label="Properties (key: value format, one per line)",
            placeholder="wood: Oak\ncore: Phoenix Feather\nlength: 11.5",
            default=props_text.strip(),
            max_length=1000,
            style=discord.TextStyle.paragraph,
            required=False
        )
        self.add_item(self.properties_input)

    async def on_submit(self, interaction: discord.Interaction):
        try:
            properties = {}
            if self.properties_input.value.strip():
                lines = self.properties_input.value.strip().split('\n')
                for line in lines:
                    if ':' in line:
                        key, value = line.split(':', 1)
                        properties[key.strip()] = value.strip()

            self.view.properties = properties
            embed = self.view.create_embed()
            await interaction.response.edit_message(embed=embed, view=self.view)

        except Exception as e:
            await interaction.response.send_message(
                "Invalid properties format! Please use 'key: value' format, one per line.",
                ephemeral=True
            )

@edit_item.autocomplete('item_name')
async def item_name_autocomplete(interaction: discord.Interaction, current: str):
    try:
        with bank.get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT name FROM shop_items WHERE name LIKE ? ORDER BY name LIMIT 25',
                     (f'%{current}%',))
            items = c.fetchall()

            return [
                app_commands.Choice(name=item[0], value=item[0])
                for item in items
            ]

    except Exception as e:
        print(f"Error in item name autocomplete: {e}")
        return [app_commands.Choice(name="Error loading items", value="Error")]

@commands.cooldown(1, 3, commands.BucketType.user)  # 1 use per 3 seconds
@bot.tree.command(name="buy_vip", description="Purchase a VIP membership for cooldown notifications")
@app_commands.guild_only()
@app_commands.describe(tier="Select your VIP membership tier")
@custom_cooldown(1, 3, "user")  # 1 use per 3 seconds
async def buy_vip(interaction: discord.Interaction, tier: Literal["weekly", "monthly", "quarterly"]):
    try:
        # Use deferred response to prevent timeout
        await defer_response(interaction, ephemeral=True)
        
        user_id = str(interaction.user.id)
        username = interaction.user.display_name
        
        # Check if the tier is valid
        if tier not in bank.VIP_TIERS:
            await send_message(
                interaction,
                embed=Embed(
                    title="❌ Invalid VIP Tier",
                    description="The selected VIP tier is not valid.",
                    color=bank.error_color
                ),
                ephemeral=True
            )
            return
        
        # Get tier details
        tier_info = bank.VIP_TIERS[tier]
        price = tier_info["price"]
        name = tier_info["name"]
        duration = tier_info["duration"]
        
        # Check if premium tier
        is_premium = tier == "quarterly"
        vip_badge = bank.premium_vip_emoji if is_premium else bank.vip_emoji
        
        # Check if user already has VIP
        vip_info = bank.get_vip_info(user_id)
        is_extending = vip_info is not None
        
        # Check if user has enough funds
        user_balance = bank.get_balance(user_id)
        if user_balance < price:
            await send_message(
                interaction,
                embed=Embed(
                    title="❌ Insufficient Funds",
                    description=f"You can't afford {name}! It costs {bank.format_currency(price)}.\n"
                                f"Your current balance: {bank.format_currency(user_balance)}",
                    color=bank.error_color
                ),
                ephemeral=True
            )
            return
        
        # Create confirmation view for purchasing
        class ConfirmPurchase(View):
            def __init__(self):
                super().__init__(timeout=60)
            
            @discord.ui.button(label="Confirm Purchase", style=discord.ButtonStyle.green)
            async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
                try:
                    # Disable all buttons immediately to prevent multiple submissions
                    for item in self.children:
                        item.disabled = True
                    await interaction.response.edit_message(view=self)
                    
                    # Process purchase in a single atomic transaction
                    with bank.get_db_connection() as conn:
                        cursor = conn.cursor()
                        try:
                            # Start transaction
                            cursor.execute('BEGIN TRANSACTION')
                            
                            # 1. Verify balance again (in case it changed)
                            cursor.execute('''
                                SELECT (galleons * ? + sickles * ? + knuts) as total_knuts 
                                FROM users WHERE user_id = ?
                            ''', (bank.KNUTS_PER_GALLEON, bank.KNUTS_PER_SICKLE, user_id))
                            
                            current_balance = cursor.fetchone()
                            if not current_balance or current_balance[0] < price:
                                # Not enough funds
                                conn.rollback()
                                
                                error_embed = Embed(
                                    title="❌ Insufficient Funds",
                                    description="Your balance has changed and you can no longer afford this membership.",
                                    color=bank.error_color
                                )
                                await edit_message(
                                    interaction,
                                    embed=error_embed,
                                    view=None
                                )
                                return
                            
                            # 2. Deduct the cost
                            galleons, sickles, knuts = bank.convert_to_all_denominations(current_balance[0] - price)
                            
                            cursor.execute('''
                                UPDATE users
                                SET galleons = ?,
                                    sickles = ?,
                                    knuts = ?
                                WHERE user_id = ?
                            ''', (galleons, sickles, knuts, user_id))
                            
                            # 3. Add or update VIP membership
                            now = datetime.now()
                            
                            if is_extending:
                                # Get current expiry
                                cursor.execute('''
                                    SELECT expiry_date 
                                    FROM vip_memberships 
                                    WHERE user_id = ?
                                ''', (user_id,))
                                
                                result = cursor.fetchone()
                                if result:
                                    # Calculate new expiry date
                                    current_expiry = datetime.fromisoformat(result[0].replace('Z', '+00:00'))
                                    
                                    # If expired, start from now
                                    if current_expiry < now:
                                        expiry_date = now + timedelta(days=duration)
                                    else:
                                        expiry_date = current_expiry + timedelta(days=duration)
                                    
                                    # Update membership
                                    cursor.execute('''
                                        UPDATE vip_memberships
                                        SET tier = ?,
                                            expiry_date = ?
                                        WHERE user_id = ?
                                    ''', (tier, expiry_date.isoformat(), user_id))
                                else:
                                    # This shouldn't happen, but handle it anyway
                                    expiry_date = now + timedelta(days=duration)
                                    
                                    cursor.execute('''
                                        INSERT INTO vip_memberships (user_id, tier, start_date, expiry_date)
                                        VALUES (?, ?, ?, ?)
                                    ''', (user_id, tier, now.isoformat(), expiry_date.isoformat()))
                            else:
                                # New membership
                                expiry_date = now + timedelta(days=duration)
                                cursor.execute('DELETE FROM vip_memberships WHERE user_id = ? AND expiry_date <= ?', (user_id, now.isoformat()))
                                cursor.execute('''
                                    INSERT INTO vip_memberships (user_id, tier, start_date, expiry_date)
                                    VALUES (?, ?, ?, ?)
                                ''', (user_id, tier, now.isoformat(), expiry_date.isoformat()))
                            
                            # 4. Log the transaction
                            cursor.execute('''
                                INSERT INTO transactions (user_id, amount, type, timestamp, modifier_id, details)
                                VALUES (?, ?, ?, ?, ?, ?)
                            ''', (
                                user_id,
                                -price,
                                'vip_purchase',
                                now.isoformat(),
                                user_id,
                                f"Purchased {name} VIP membership" if not is_extending else f"Extended VIP membership with {name}"
                            ))
                            
                            # Commit the transaction
                            conn.commit()
                            
                            # Log the purchase
                            logger.info(f"VIP Purchase: User {user_id} ({username}) purchased {name} for {price} knuts")
                            
                            # Create success embed
                            success_embed = Embed(
                                title=f"{vip_badge} VIP Membership {'Extended' if is_extending else 'Purchased'}!",
                                description=f"You have successfully {'extended your' if is_extending else 'purchased a'} **{name}** membership!",
                                color=bank.vip_color
                            )
                            
                            # Add expiry info
                            success_embed.add_field(
                                name="Expiry Date",
                                value=f"Your membership will expire on **{expiry_date.strftime('%B %d, %Y')}**",
                                inline=False
                            )
                            
                            # Add benefits info
                            success_embed.add_field(
                                name="Benefits",
                                value="• **Cooldown Notifications** - Get notified when your cooldowns expire\n"
                                      "• **Priority Support** - Get faster support from our staff\n" +
                                      ("• **Premium Badge** - Show off your premium status" if is_premium else ""),
                                inline=False
                            )
                            
                            # Use followup instead of edit_message for the second response
                            await interaction.followup.edit_message(
                                message_id=interaction.message.id,
                                embed=success_embed,
                                view=self  # Keep the disabled buttons visible
                            )
                            
                            # Send log to log channel
                            log_embed = Embed(
                                title=f"{vip_badge} VIP Membership {('Extended' if is_extending else 'Purchased')}",
                                description=f"{interaction.user.mention} has {('extended' if is_extending else 'purchased')} a {name} membership",
                                color=bank.vip_color,
                                timestamp=datetime.now()
                            )
                            log_embed.add_field(
                                name="Details",
                                value=f"**Price:** {bank.format_currency(price)}\n"
                                      f"**Duration:** {duration} days\n"
                                      f"**Expires:** <t:{int(expiry_date.timestamp())}:f>",
                                inline=False
                            )
                            log_embed.set_footer(text=f"User ID: {user_id}")
                            
                            await bank.log_to_channel(bot, log_embed)
                            
                        except Exception as tx_error:
                            # Rollback on error
                            conn.rollback()
                            print(f"Transaction error in VIP purchase: {tx_error}")
                            raise
                            
                except Exception as e:
                    print(f"Error processing VIP purchase: {e}")
                    error_embed = Embed(
                        title="❌ Purchase Failed",
                        description="An error occurred while processing your purchase.",
                        color=bank.error_color
                    )
                    try:
                        # Try to use followup instead if the response was already sent
                        await interaction.followup.edit_message(
                            message_id=interaction.message.id,
                            embed=error_embed,
                            view=self  # Keep the disabled buttons visible
                        )
                    except Exception as followup_error:
                        print(f"Error sending followup: {followup_error}")
                        # If that fails too, try a new followup message
                        await interaction.followup.send(
                            embed=error_embed,
                            ephemeral=True
                        )
            
            @discord.ui.button(label="Cancel", style=discord.ButtonStyle.grey)
            async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
                # Disable all buttons immediately to prevent multiple submissions
                for item in self.children:
                    item.disabled = True
                
                cancel_embed = Embed(
                    title="❌ Purchase Cancelled",
                    description="Your purchase has been cancelled.",
                    color=bank.error_color
                )
                try:
                    await interaction.response.edit_message(
                        embed=cancel_embed,
                        view=self
                    )
                except:
                    try:
                        await interaction.followup.edit_message(
                            interaction.message.id,
                            embed=cancel_embed,
                            view=self
                        )
                    except:
                        await interaction.followup.send(
                            embed=cancel_embed,
                            ephemeral=False
                        )
        
        # Create confirmation message
        confirm_embed = Embed(
            title=f"{vip_badge} VIP Membership Purchase",
            description=f"Are you sure you want to {('extend' if is_extending else 'purchase')} {name} membership?",
            color=bank.vip_color
        )
        
        # Create benefits list for confirmation
        benefits = [
            "• DM notifications when cooldowns expire",
            "• VIP badge on your profile"
        ]
        
        # Add premium badge benefit for quarterly members
        if is_premium:
            benefits.append(f"• Exclusive {bank.premium_vip_emoji} Premium VIP badge")
            
        benefits.append("• More benefits coming soon")
        
        confirm_embed.add_field(
            name="Details",
            value=f"**Price:** {bank.format_currency(price)}\n"
                  f"**Duration:** {duration} days\n"
                  f"**Benefits:**\n" + "\n".join(benefits),
            inline=False
        )
        
        confirm_embed.add_field(
            name="Your Balance",
            value=bank.format_currency(user_balance),
            inline=False
        )
        
        if is_extending:
            confirm_embed.add_field(
                name="Current Membership",
                value=f"You currently have **{vip_info['name']}** which expires in "
                      f"**{vip_info['days_remaining']}** days and **{vip_info['hours_remaining']}** hours.\n"
                      f"Purchasing now will extend your membership.",
                inline=False
            )
        
        await interaction.followup.send(
            embed=confirm_embed,
            view=ConfirmPurchase(),
            ephemeral=True
        )
        
    except Exception as e:
        print(f"Error in buy_vip command: {e}")
        error_embed = Embed(
            title="❌ Error",
            description="An error occurred while processing your VIP purchase request.",
            color=bank.error_color
        )
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(embed=error_embed, ephemeral=True)
            else:
                await interaction.followup.send(embed=error_embed, ephemeral=True)
        except Exception as inner_e:
            print(f"Failed to send error message: {inner_e}")

@bot.tree.command(name="vip_status", description="Check your VIP membership status")
@app_commands.guild_only()
@custom_cooldown(1, 3, "user")  # 1 use per 3 seconds
async def vip_status(interaction: discord.Interaction):
    try:
        # Use deferred response to prevent timeout
        await defer_response(interaction, ephemeral=True)
        
        user_id = str(interaction.user.id)
        
        # Get VIP info
        vip_info = bank.get_vip_info(user_id)
        
        if not vip_info:
            # User doesn't have VIP
            embed = Embed(
                title="❌ No VIP Membership",
                description="You don't currently have a VIP membership.",
                color=bank.info_color
            )
            
            # Show available tiers
            embed.add_field(
                name="Available VIP Tiers",
                value=(
                    f"• **Weekly**: {bank.format_currency(bank.VIP_TIERS['weekly']['price'])} - 7 days\n"
                    f"• **Monthly**: {bank.format_currency(bank.VIP_TIERS['monthly']['price'])} - 30 days\n"
                    f"• **Quarterly**: {bank.format_currency(bank.VIP_TIERS['quarterly']['price'])} - 90 days"
                ),
                inline=False
            )
            
            embed.add_field(
                name="VIP Benefits",
                value=(
                    "• **Cooldown Notifications** - Get notified when your cooldowns expire\n"
                    "• **Priority Support** - Get faster support from our staff\n"
                    "• **Premium Badge** - Show off your premium status (Quarterly tier only)"
                ),
                inline=False
            )
            
            embed.set_footer(text="Use /buy_vip to purchase a membership")
            
            await send_message(interaction, embed=embed, ephemeral=True)
            return
        
        # User has VIP, get details
        tier = vip_info["tier"]
        expiry_date = datetime.fromisoformat(vip_info["expiry_date"].replace('Z', '+00:00'))
        start_date = datetime.fromisoformat(vip_info["start_date"].replace('Z', '+00:00'))
        now = datetime.now()
        
        # Check if expired
        is_expired = expiry_date < now
        
        # Get remaining time or time since expiry
        if is_expired:
            time_diff = now - expiry_date
            status_text = f"Expired **{time_diff.days}** days ago"
            status_color = bank.error_color
        else:
            time_diff = expiry_date - now
            days_remaining = time_diff.days
            hours_remaining = time_diff.seconds // 3600
            
            if days_remaining > 0:
                status_text = f"**{days_remaining}** days and **{hours_remaining}** hours remaining"
            else:
                minutes_remaining = (time_diff.seconds % 3600) // 60
                status_text = f"**{hours_remaining}** hours and **{minutes_remaining}** minutes remaining"
            
            # Set color based on time remaining
            if days_remaining < 1:
                status_color = bank.error_color  # Red for < 1 day
            elif days_remaining < 3:
                status_color = Color.orange()  # Orange for < 3 days
            else:
                status_color = bank.vip_color  # Normal VIP color
        
        # Get tier info
        tier_name = bank.VIP_TIERS[tier]["name"]
        is_premium = tier == "quarterly"
        vip_badge = bank.premium_vip_emoji if is_premium else bank.vip_emoji
        
        # Create embed
        embed = Embed(
            title=f"{vip_badge} VIP Membership Status",
            description=f"You have an {'**EXPIRED**' if is_expired else 'active'} **{tier_name}** membership.",
            color=status_color
        )
        
        # Add expiry info
        embed.add_field(
            name="Status",
            value=status_text,
            inline=False
        )
        
        # Add dates
        embed.add_field(
            name="Purchase Date",
            value=start_date.strftime("%B %d, %Y"),
            inline=True
        )
        
        embed.add_field(
            name="Expiry Date",
            value=expiry_date.strftime("%B %d, %Y"),
            inline=True
        )
        
        # Add renewal info if expired or about to expire
        if is_expired or days_remaining < 7:
            embed.add_field(
                name="Renewal",
                value=(
                    f"Your membership {'has expired' if is_expired else 'is expiring soon'}!\n"
                    f"Use `/buy_vip tier:{tier}` to {'renew' if is_expired else 'extend'} your membership."
                ),
                inline=False
            )
        
        # Add benefits
        benefits = [
            "• **Cooldown Notifications** - Get notified when your cooldowns expire",
            "• **Priority Support** - Get faster support from our staff"
        ]
        
        if is_premium:
            benefits.append("• **Premium Badge** - You have the premium VIP badge")
        
        embed.add_field(
            name="Your Benefits",
            value="\n".join(benefits),
            inline=False
        )
        
        # Send the embed
        await send_message(interaction, embed=embed, ephemeral=True)
        
    except Exception as e:
        logger.error(f"Error in vip_status command: {e}")
        error_embed = Embed(
            title="❌ Error",
            description="An error occurred while fetching your VIP status.",
            color=bank.error_color
        )
        await send_message(interaction, embed=error_embed, ephemeral=True)

@bot.tree.command(name="admin_vip", description="Grant or revoke VIP membership for a user (Bank Masters only)")
@app_commands.guild_only()
@app_commands.describe(
    action="Action to perform",
    user="User to modify",
    tier="VIP tier (required when granting)"
)
async def admin_vip(
    interaction: discord.Interaction,
    action: Literal["grant", "revoke"],
    user: discord.Member,
    tier: Optional[Literal["weekly", "monthly", "quarterly"]] = None
):
    try:
        # Defer response to prevent timeout
        await interaction.response.defer(ephemeral=False)

        # Check if user has banker role
        has_permission = False
        for role in interaction.user.roles:
            if role.id in bank.banker_roles:
                has_permission = True
                break
        
        if not has_permission and interaction.user.id != bot.owner_id:
            embed = Embed(
                title="❌ Access Denied",
                description="You don't have permission to manage VIP memberships!",
                color=bank.error_color
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return
        
        target_id = str(user.id)

        # Process based on action
        if action == "grant":
            # Check if tier is provided
            if not tier:
                await interaction.followup.send(
                    "You must specify a tier when granting VIP membership!",
                    ephemeral=True
                )
                return

            # Grant VIP membership
            success = bank.add_vip_membership(target_id, tier)
            
            if success:
                # Get VIP info
                vip_info = bank.get_vip_info(target_id)
                expiry_timestamp = int(datetime.fromisoformat(vip_info['expiry_date'].replace('Z', '+00:00')).timestamp())
                
                # Send success message
                embed = Embed(
                    title=f"{bank.vip_emoji} VIP Membership Granted",
                    description=f"Successfully granted {vip_info['name']} to {user.mention}",
                    color=bank.success_color
                )
                
                embed.add_field(
                    name="Membership Details",
                    value=f"**Tier:** {vip_info['name']}\n"
                          f"**Expires:** <t:{expiry_timestamp}:f>",
                    inline=False
                )
                
                # Log to channel
                log_embed = Embed(
                    title=f"{bank.vip_emoji} VIP Membership Granted",
                    description=f"{interaction.user.mention} granted VIP membership to {user.mention}",
                    color=bank.vip_color,
                    timestamp=datetime.now()
                )
                log_embed.add_field(
                    name="Details",
                    value=f"**Tier:** {vip_info['name']}\n"
                          f"**Expires:** <t:{expiry_timestamp}:f>",
                    inline=False
                )
                log_embed.set_footer(text=f"Granted by: {interaction.user.id}")
                
                await bank.log_to_channel(bot, log_embed)
            else:
                embed = Embed(
                    title="❌ Error",
                    description=f"Failed to grant VIP membership to {user.mention}",
                    color=bank.error_color
                )
        
        else:  # revoke
            # Delete from VIP memberships
            with bank.get_db_connection() as conn:
                c = conn.cursor()
                # Check if user has VIP first
                c.execute('SELECT 1 FROM vip_memberships WHERE user_id = ?', (target_id,))
                has_vip = bool(c.fetchone())
                
                if has_vip:
                    c.execute('DELETE FROM vip_memberships WHERE user_id = ?', (target_id,))
                    conn.commit()
                    
                    # Create success message
                    embed = Embed(
                        title="VIP Membership Revoked",
                        description=f"Successfully revoked VIP membership from {user.mention}",
                        color=bank.success_color
                    )
                    
                    # Log to channel
                    log_embed = Embed(
                        title="VIP Membership Revoked",
                        description=f"{interaction.user.mention} revoked VIP membership from {user.mention}",
                        color=bank.info_color,
                        timestamp=datetime.now()
                    )
                    log_embed.set_footer(text=f"Revoked by: {interaction.user.id}")
                    
                    await bank.log_to_channel(bot, log_embed)
                else:
                    embed = Embed(
                        title="❌ No Action Taken",
                        description=f"{user.mention} does not have an active VIP membership",
                        color=bank.error_color
                    )
        
        await interaction.followup.send(embed=embed, ephemeral=False)
        
    except Exception as e:
        print(f"Error in admin_vip command: {e}")
        error_embed = Embed(
            title="❌ Error",
            description=f"An error occurred: {str(e)}",
            color=bank.error_color
        )
        await interaction.followup.send(embed=error_embed, ephemeral=True)

# Add an admin command to list all VIP members
@bot.tree.command(name="list_vip", description="List all active VIP members (Bank Masters only)")
@app_commands.guild_only()
async def list_vip(interaction: discord.Interaction):
    try:
        # Defer response
        await interaction.response.defer(ephemeral=False)
        
        # Check if user has banker role
        has_permission = False
        for role in interaction.user.roles:
            if role.id in bank.banker_roles:
                has_permission = True
                break
        
        if not has_permission and interaction.user.id != bot.owner_id:
            embed = Embed(
                title="❌ Access Denied",
                description="You don't have permission to view VIP members!",
                color=bank.error_color
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return
        
        # Get all VIP members
        with bank.get_db_connection() as conn:
            c = conn.cursor()
            c.execute('''
                SELECT v.user_id, v.tier, v.expiry_date, u.username 
                FROM vip_memberships v
                JOIN users u ON v.user_id = u.user_id
                WHERE v.expiry_date > datetime('now')
                ORDER BY v.expiry_date ASC
            ''')
            vip_members = c.fetchall()
        
        if not vip_members:
            await interaction.followup.send("No active VIP members found.", ephemeral=False)
            return
        
        # Create paginated embeds (10 members per page)
        embeds = []
        members_per_page = 10
        
        for i in range(0, len(vip_members), members_per_page):
            page_members = vip_members[i:i+members_per_page]
            
            embed = Embed(
                title=f"{bank.vip_emoji} VIP Members",
                description=f"Showing {i+1}-{min(i+members_per_page, len(vip_members))} of {len(vip_members)} VIP members",
                color=bank.vip_color
            )
            
            for user_id, tier, expiry_date, username in page_members:
                # Format expiry date as Discord timestamp
                expiry_time = datetime.fromisoformat(expiry_date.replace('Z', '+00:00'))
                expiry_ts = int(expiry_time.timestamp())
                
                # Calculate days remaining
                now = datetime.now()
                days_remaining = (expiry_time - now).days
                
                # Get tier emoji
                tier_emoji = bank.premium_vip_emoji if tier == "quarterly" else bank.vip_emoji
                
                embed.add_field(
                    name=f"{tier_emoji} {username}",
                    value=f"**Tier:** {bank.VIP_TIERS[tier]['name']}\n"
                          f"**Expires:** <t:{expiry_ts}:R> (<t:{expiry_ts}:f>)\n"
                          f"**Days Left:** {days_remaining}\n"
                          f"**ID:** {user_id}",
                    inline=False
                )
            
            embed.set_footer(text=f"Page {i//members_per_page + 1}/{(len(vip_members)-1)//members_per_page + 1}")
            embeds.append(embed)
        
        # Send first page
        if len(embeds) == 1:
            await interaction.followup.send(embed=embeds[0], ephemeral=False)
        else:
            # Create pagination buttons for multiple pages
            class PaginationView(View):
                def __init__(self):
                    super().__init__(timeout=60)
                    self.current_page = 0
                
                @discord.ui.button(label="◀️ Previous", style=discord.ButtonStyle.grey)
                async def prev_button(self, interaction: discord.Interaction, button: discord.ui.Button):
                    self.current_page = (self.current_page - 1) % len(embeds)
                    await interaction.response.edit_message(embed=embeds[self.current_page], view=self)
                
                @discord.ui.button(label="Next ▶️", style=discord.ButtonStyle.grey)
                async def next_button(self, interaction: discord.Interaction, button: discord.ui.Button):
                    self.current_page = (self.current_page + 1) % len(embeds)
                    await interaction.response.edit_message(embed=embeds[self.current_page], view=self)
            
            await interaction.followup.send(embed=embeds[0], view=PaginationView(), ephemeral=False)
            
    except Exception as e:
        print(f"Error in list_vip command: {e}")
        error_embed = Embed(
            title="❌ Error",
            description=f"An error occurred: {str(e)}",
            color=bank.error_color
        )
        await interaction.followup.send(embed=error_embed, ephemeral=False)

@bot.tree.command(name="reset_cooldown", description="Reset cooldowns for a user (Bank Masters only)")
@app_commands.guild_only()
@app_commands.describe(
    user="The user whose cooldown to reset",
    cooldown_type="Type of cooldown to reset"
)
async def reset_cooldown(
    interaction: discord.Interaction,
    user: discord.Member,
    cooldown_type: Literal["work_cooldown", "income_cooldown", "all"]
):
    try:
        # Defer response to prevent timeouts
        await interaction.response.defer(ephemeral=False)
        
        # Check if user has banker role
        has_permission = False
        
        # Check if interaction.user is a Member with roles
        if hasattr(interaction.user, 'roles'):
            for role in interaction.user.roles:
                if role.id in bank.banker_roles:
                    has_permission = True
                    break
        else:
            logger.warning(f"User {interaction.user.id} ({interaction.user.display_name}) doesn't have roles attribute in reset_cooldown command")
        
        if not has_permission and interaction.user.id != bot.owner_id:
            embed = Embed(
                title="❌ Access Denied",
                description="You don't have permission to reset cooldowns!",
                color=bank.error_color
            )
            await interaction.followup.send(embed=embed, ephemeral=False)
            return
        
        target_id = str(user.id)
        
        # Get current cooldowns for logging
        old_work_cooldown = bank.get_cooldown(target_id, 'work_cooldown')
        old_income_cooldown = bank.get_cooldown(target_id, 'income_cooldown')
        
        # Determine which cooldowns to reset
        reset_work = cooldown_type in ["work_cooldown", "all"]
        reset_income = cooldown_type in ["income_cooldown", "all"]
        
        with bank.get_db_connection() as conn:
            c = conn.cursor()
            
            # Start transaction
            c.execute('BEGIN TRANSACTION')
            
            try:
                # Reset appropriate cooldowns
                if reset_work:
                    # Remove the work cooldown by setting to NULL
                    c.execute('''
                        INSERT INTO cooldowns (user_id, work_cooldown)
                        VALUES (?, NULL)
                        ON CONFLICT(user_id)
                        DO UPDATE SET work_cooldown = NULL
                    ''', (target_id,))
                
                if reset_income:
                    # Remove the income cooldown by setting to NULL
                    c.execute('''
                        INSERT INTO cooldowns (user_id, income_cooldown)
                        VALUES (?, NULL)
                        ON CONFLICT(user_id)
                        DO UPDATE SET income_cooldown = NULL
                    ''', (target_id,))
                
                # Commit changes
                conn.commit()
                
                # Create response embed
                embed = Embed(
                    title="⏰ Cooldown Reset",
                    description=f"Successfully reset cooldowns for {user.mention}",
                    color=bank.success_color
                )
                
                # Add details about which cooldowns were reset
                details = []
                if reset_work:
                    details.append("Work cooldown")
                if reset_income:
                    details.append("Income cooldown")
                
                embed.add_field(
                    name="Reset Cooldowns",
                    value="\n".join([f"• {detail}" for detail in details]),
                    inline=False
                )
                
                # Log the action
                log_embed = Embed(
                    title="⏰ Cooldown Reset Log",
                    description=f"{interaction.user.mention} reset cooldowns for {user.mention}",
                    color=bank.info_color,
                    timestamp=datetime.now()
                )
                
                # Add details about old cooldowns
                if reset_work and old_work_cooldown:
                    try:
                        old_time = datetime.fromisoformat(old_work_cooldown)
                        time_str = old_time.strftime("%Y-%m-%d %H:%M:%S")
                        log_embed.add_field(
                            name="Old Work Cooldown",
                            value=time_str,
                            inline=True
                        )
                    except:
                        log_embed.add_field(
                            name="Old Work Cooldown",
                            value=str(old_work_cooldown),
                            inline=True
                        )
                
                if reset_income and old_income_cooldown:
                    try:
                        old_time = datetime.fromisoformat(old_income_cooldown)
                        time_str = old_time.strftime("%Y-%m-%d %H:%M:%S")
                        log_embed.add_field(
                            name="Old Income Cooldown",
                            value=time_str,
                            inline=True
                        )
                    except:
                        log_embed.add_field(
                            name="Old Income Cooldown",
                            value=str(old_income_cooldown),
                            inline=True
                        )
                
                log_embed.set_footer(text=f"Reset by: {interaction.user.id}")
                
                # Send to log channel
                await bank.log_to_channel(bot, log_embed)
                
                # Check if user has VIP membership
                is_vip = bank.is_vip(target_id)
                if is_vip:
                    # For VIP users, cancel pending notifications since cooldown is reset
                    try:
                        if reset_work and vip_notifications:
                            # Cancel pending work notifications
                            cancelled_work = bank.cancel_pending_notifications(target_id, "work")
                            if cancelled_work > 0:
                                logger.info(f"Cancelled {cancelled_work} pending work notifications for VIP user {target_id}")

                        if reset_income and vip_notifications:
                            # Cancel pending income notifications
                            cancelled_income = bank.cancel_pending_notifications(target_id, "income")
                            if cancelled_income > 0:
                                logger.info(f"Cancelled {cancelled_income} pending income notifications for VIP user {target_id}")

                        embed.add_field(
                            name="VIP Notifications",
                            value="Cancelled pending notifications for VIP user",
                            inline=False
                        )
                    except Exception as vip_err:
                        print(f"Error updating VIP notifications: {vip_err}")
                
                await interaction.followup.send(embed=embed, ephemeral=False)
                
                # Optionally notify the user that their cooldowns were reset
                try:
                    target_user = await bot.fetch_user(int(target_id))
                    if target_user:
                        notification = Embed(
                            title="⏰ Cooldowns Reset",
                            description="Good news! Your cooldowns have been reset by a bank master.",
                            color=bank.success_color
                        )
                        
                        # Add details about which cooldowns were reset
                        notification.add_field(
                            name="Reset Cooldowns",
                            value="\n".join([f"• {detail}" for detail in details]),
                            inline=False
                        )
                        
                        await target_user.send(embed=notification)
                except Exception as notify_err:
                    print(f"Error notifying user about cooldown reset: {notify_err}")
                    # Continue even if notification fails
                
            except Exception as e:
                conn.rollback()
                print(f"Error resetting cooldowns: {e}")
                
                error_embed = Embed(
                    title="❌ Error",
                    description=f"An error occurred while resetting cooldowns: {str(e)}",
                    color=bank.error_color
                )
                await interaction.followup.send(embed=error_embed, ephemeral=False)
                
    except Exception as e:
        print(f"Error in reset_cooldown command: {e}")
        error_embed = Embed(
            title="❌ Error",
            description=f"An error occurred: {str(e)}",
            color=bank.error_color
        )
        try:
            await interaction.followup.send(embed=error_embed, ephemeral=False)
        except:
            if not interaction.response.is_done():
                await interaction.response.send_message(embed=error_embed, ephemeral=False)

@bot.tree.command(name="bulk_modify_balance", description="Modify multiple users' balances at once (Bank Masters only)")
@app_commands.guild_only()
@app_commands.describe(
    role="Role whose members will receive the balance modification",
    amount="Amount to add/remove",
    currency="Currency type (Galleons, Sickles, or Knuts)",
    reason="Reason for the balance modification (optional)"
)
async def bulk_modify_balance(
    interaction: discord.Interaction,
    role: discord.Role,
    amount: int, 
    currency: Literal["Galleons", "Sickles", "Knuts"],
    reason: Optional[str] = "No reason provided"
):
    # Use deferred response to prevent timeouts
    await interaction.response.defer(ephemeral=False)
    
    # Check if user has banker role
    has_permission = False
    
    # Check if interaction.user is a Member with roles
    if hasattr(interaction.user, 'roles'):
        for role_check in interaction.user.roles:
            if role_check.id in bank.banker_roles:
                has_permission = True
                break
    else:
        logger.warning(f"User {interaction.user.id} ({interaction.user.display_name}) doesn't have roles attribute in bulk_modify_balance command")
    
    if not has_permission and interaction.user.id != bot.owner_id:
        embed = Embed(
            title="❌ Access Denied",
            description="You don't have permission to modify user balances!",
            color=bank.error_color
        )
        await interaction.followup.send(embed=embed, ephemeral=False)
        return
    
    # Convert to knuts
    if currency == "Galleons":
        knuts_amount = amount * bank.KNUTS_PER_GALLEON
    elif currency == "Sickles":
        knuts_amount = amount * bank.KNUTS_PER_SICKLE
    else:
        knuts_amount = amount
    
    # Get all members with the role
    valid_users = []
    for member in interaction.guild.members:
        if role in member.roles:
            valid_users.append((str(member.id), member.display_name))
    
    if not valid_users:
        embed = Embed(
            title="❌ No Users Found",
            description=f"No users with the role {role.mention} were found.",
            color=bank.error_color
        )
        await interaction.followup.send(embed=embed, ephemeral=False)
        return
    
    # Create confirmation view
    class ConfirmView(discord.ui.View):
        def __init__(self):
            super().__init__(timeout=60)
        
        @discord.ui.button(label="Confirm", style=discord.ButtonStyle.green)
        async def confirm_button(self, interaction: discord.Interaction, button: discord.ui.Button):
            # Update balances for all valid users
            await process_balance_updates(interaction, valid_users, knuts_amount, amount, currency, reason)
        
        @discord.ui.button(label="Cancel", style=discord.ButtonStyle.grey)
        async def cancel_button(self, interaction: discord.Interaction, button: discord.ui.Button):
            cancel_embed = Embed(
                title="❌ Modification Cancelled",
                description="Bulk balance modification has been cancelled.",
                color=bank.error_color
            )
            await interaction.response.edit_message(embed=cancel_embed, view=None)
    
    # Create confirmation embed
    confirm_embed = Embed(
        title="💰 Confirm Bulk Balance Modification",
        description=f"You are about to modify the balance of {len(valid_users)} users with the {role.mention} role.",
        color=bank.info_color
    )
    
    # Format amount display
    if amount >= 0:
        amount_str = f"+{amount} {currency}\n(+{bank.format_currency(knuts_amount)})"
    else:
        amount_str = f"-{abs(amount)} {currency}\n(-{bank.format_currency(abs(knuts_amount))})"
    
    confirm_embed.add_field(
        name="Amount",
        value=amount_str,
        inline=False
    )
    
    confirm_embed.add_field(
        name="Reason",
        value=reason,
        inline=False
    )
    
    # Display list of users to be modified (limit to 15)
    display_users = valid_users[:15]
    user_list = [f"**{username}**" for _, username in display_users]
    
    if len(valid_users) > 15:
        user_list.append(f"...and {len(valid_users) - 15} more users")
    
    confirm_embed.add_field(
        name=f"Users with {role.name} Role",
        value="\n".join(user_list),
        inline=False
    )
    
    await interaction.followup.send(embed=confirm_embed, view=ConfirmView(), ephemeral=True)

# Helper function to process balance updates for bulk operations
async def process_balance_updates(interaction, valid_users, knuts_amount, amount, currency, reason):
    # Update balances for all valid users
    modified_users = []
    failed_users = []
    
    with bank.get_db_connection() as conn:
        c = conn.cursor()
        
        for user_id, username in valid_users:
            try:
                # Get old balance for logging
                old_balance = bank.get_balance(user_id)
                
                # Update balance
                bank.update_balance(user_id, knuts_amount, username)
                
                # Log admin modification
                bank.log_transaction(
                    user_id,
                    knuts_amount,
                    'admin',
                    str(interaction.user.id),
                    f"Bulk balance modification: {amount} {currency} ({reason})"
                )
                
                # Get new balance
                new_balance = bank.get_balance(user_id)
                
                # Add to successful modifications
                modified_users.append({
                    "user_id": user_id,
                    "username": username,
                    "old_balance": old_balance,
                    "new_balance": new_balance
                })
                
            except Exception as e:
                print(f"Error modifying balance for {user_id}: {e}")
                failed_users.append((user_id, username, str(e)))
    
    # Create response embed
    if modified_users:
        # Format the description with appropriate sign
        if knuts_amount >= 0:
            description = f"Modified balance for {len(modified_users)} users by +{bank.format_currency(knuts_amount)}"
        else:
            description = f"Modified balance for {len(modified_users)} users by -{bank.format_currency(abs(knuts_amount))}"
            
        embed = Embed(
            title="💰 Bulk Balance Modification",
            description=description,
            color=bank.success_color
        )
        
        # Format amount display in the details
        if amount >= 0:
            amount_str = f"**Amount:** +{amount} {currency}"
        else:
            amount_str = f"**Amount:** -{abs(amount)} {currency}"
        
        embed.add_field(
            name="Modification Details",
            value=f"{amount_str}\n**Reason:** {reason}",
            inline=False
        )
        
        # List the first 10 modified users
        display_users = modified_users[:10]
        user_list = []
        
        for user in display_users:
            user_list.append(f"**{user['username']}**\n"
                           f"Old: {bank.format_currency_short(user['old_balance'])} → "
                           f"New: {bank.format_currency_short(user['new_balance'])}")
        
        if len(modified_users) > 10:
            user_list.append(f"...and {len(modified_users) - 10} more users")
        
        embed.add_field(
            name="Modified Users",
            value="\n".join(user_list),
            inline=False
        )
        
        if failed_users:
            failed_list = [f"**{username}** - {error}" for user_id, username, error in failed_users[:5]]
            if len(failed_users) > 5:
                failed_list.append(f"...and {len(failed_users) - 5} more users")
            
            embed.add_field(
                name="Failed Modifications",
                value="\n".join(failed_list),
                inline=False
            )
        
        # Create log embed
        log_embed = Embed(
            title="💰 Bulk Balance Modification",
            description=f"{interaction.user.mention} modified balance for {len(modified_users)} users",
            color=bank.info_color,
            timestamp=datetime.now()
        )
        
        # Format amount for log embed
        if knuts_amount >= 0:
            log_amount_str = f"+{bank.format_currency(knuts_amount)}"
        else:
            log_amount_str = f"-{bank.format_currency(abs(knuts_amount))}"
        
        log_embed.add_field(
            name="Modification Details",
            value=f"**Amount:** {log_amount_str}\n"
                  f"**Reason:** {reason}",
            inline=False
        )
        
        log_embed.add_field(
            name="User Count",
            value=f"**Modified:** {len(modified_users)}\n"
                  f"**Failed:** {len(failed_users)}",
            inline=False
        )
        
        log_embed.set_footer(text=f"Modified by: {interaction.user.id}")
        
        # Send to log channel
        await bank.log_to_channel(bot, log_embed)
    
    else:
        embed = Embed(
            title="❌ Bulk Modification Failed",
            description="Failed to modify any user balances.",
            color=bank.error_color
        )
        
        if failed_users:
            failed_list = [f"**{username}** - {error}" for user_id, username, error in failed_users[:10]]
            if len(failed_users) > 10:
                failed_list.append(f"...and {len(failed_users) - 10} more users")
            
            embed.add_field(
                name="Errors",
                value="\n".join(failed_list),
                inline=False
            )
    
    await interaction.response.edit_message(embed=embed, view=None)

@bot.tree.command(name="bulk_reset_cooldown", description="Reset cooldowns for multiple users at once (Bank Masters only)")
@app_commands.guild_only()
@app_commands.describe(
    role="Role whose members will have cooldowns reset",
    cooldown_type="Type of cooldown to reset"
)
async def bulk_reset_cooldown(
    interaction: discord.Interaction,
    role: discord.Role,
    cooldown_type: Literal["work_cooldown", "income_cooldown", "all"]
):
    # Use deferred response to prevent timeouts
    await interaction.response.defer(ephemeral=False)
    
    # Check if user has banker role
    has_permission = False
    
    # Check if interaction.user is a Member with roles
    if hasattr(interaction.user, 'roles'):
        for role_check in interaction.user.roles:
            if role_check.id in bank.banker_roles:
                has_permission = True
                break
    else:
        logger.warning(f"User {interaction.user.id} ({interaction.user.display_name}) doesn't have roles attribute in bulk_reset_cooldown command")
    
    if not has_permission and interaction.user.id != bot.owner_id:
        embed = Embed(
            title="❌ Access Denied",
            description="You don't have permission to reset cooldowns!",
            color=bank.error_color
        )
        await interaction.followup.send(embed=embed, ephemeral=False)
        return
    
    # Get all members with the role
    valid_users = []
    for member in interaction.guild.members:
        if role in member.roles:
            valid_users.append((str(member.id), member.display_name, member))
    
    if not valid_users:
        embed = Embed(
            title="❌ No Users Found",
            description=f"No users with the role {role.mention} were found.",
            color=bank.error_color
        )
        await interaction.followup.send(embed=embed, ephemeral=False)
        return
    
    # Create confirmation view
    class ConfirmView(discord.ui.View):
        def __init__(self):
            super().__init__(timeout=60)
        
        @discord.ui.button(label="Confirm", style=discord.ButtonStyle.green)
        async def confirm_button(self, interaction: discord.Interaction, button: discord.ui.Button):
            # Reset cooldowns for all valid users
            await process_cooldown_resets(interaction, valid_users, cooldown_type)
        
        @discord.ui.button(label="Cancel", style=discord.ButtonStyle.grey)
        async def cancel_button(self, interaction: discord.Interaction, button: discord.ui.Button):
            cancel_embed = Embed(
                title="❌ Reset Cancelled",
                description="Bulk cooldown reset has been cancelled.",
                color=bank.error_color
            )
            await interaction.response.edit_message(embed=cancel_embed, view=None)
    
    # Create confirmation embed
    reset_type_display = cooldown_type.replace('_', ' ').replace('all', 'all cooldowns')
    confirm_embed = Embed(
        title="⏰ Confirm Bulk Cooldown Reset",
        description=f"You are about to reset {reset_type_display} for {len(valid_users)} users with the {role.mention} role.",
        color=bank.info_color
    )
    
    # Display list of users to be modified (limit to 15)
    display_users = valid_users[:15]
    user_list = [f"**{username}**" for _, username, _ in display_users]
    
    if len(valid_users) > 15:
        user_list.append(f"...and {len(valid_users) - 15} more users")
    
    confirm_embed.add_field(
        name=f"Users with {role.name} Role",
        value="\n".join(user_list),
        inline=False
    )
    
    await interaction.followup.send(embed=confirm_embed, view=ConfirmView(), ephemeral=True)

# Helper function to process cooldown resets for bulk operations
async def process_cooldown_resets(interaction, valid_users, cooldown_type):
    # Reset cooldowns for all valid users
    reset_users = []
    failed_users = []
    
    # Determine which cooldowns to reset
    reset_work = cooldown_type in ["work_cooldown", "all"]
    reset_income = cooldown_type in ["income_cooldown", "all"]
    
    with bank.get_db_connection() as conn:
        c = conn.cursor()
        
        for user_id, username, user_obj in valid_users:
            try:
                # Get current cooldowns for logging
                old_work_cooldown = bank.get_cooldown(user_id, 'work_cooldown')
                old_income_cooldown = bank.get_cooldown(user_id, 'income_cooldown')
                
                # Start transaction
                c.execute('BEGIN TRANSACTION')
                
                try:
                    # Reset appropriate cooldowns
                    if reset_work:
                        # Remove the work cooldown by setting to NULL
                        c.execute('''
                            INSERT INTO cooldowns (user_id, work_cooldown)
                            VALUES (?, NULL)
                            ON CONFLICT(user_id)
                            DO UPDATE SET work_cooldown = NULL
                        ''', (user_id,))
                    
                    if reset_income:
                        # Remove the income cooldown by setting to NULL
                        c.execute('''
                            INSERT INTO cooldowns (user_id, income_cooldown)
                            VALUES (?, NULL)
                            ON CONFLICT(user_id)
                            DO UPDATE SET income_cooldown = NULL
                        ''', (user_id,))
                    
                    # Commit changes
                    conn.commit()
                    
                    # Check if user has VIP membership
                    is_vip = bank.is_vip(user_id)
                    vip_updated = False
                    
                    if is_vip and vip_notifications:
                        # Cancel pending notifications for VIP users since cooldown is reset
                        try:
                            if reset_work:
                                cancelled_work = bank.cancel_pending_notifications(user_id, "work")
                                if cancelled_work > 0:
                                    vip_updated = True
                                    logger.info(f"Cancelled {cancelled_work} pending work notifications for VIP user {user_id}")

                            if reset_income:
                                cancelled_income = bank.cancel_pending_notifications(user_id, "income")
                                if cancelled_income > 0:
                                    vip_updated = True
                                    logger.info(f"Cancelled {cancelled_income} pending income notifications for VIP user {user_id}")
                        except Exception as vip_err:
                            logger.error(f"Error updating VIP notifications for {user_id}: {vip_err}")
                    
                    # Add to successful resets
                    reset_users.append({
                        "user_id": user_id,
                        "username": username,
                        "old_work_cooldown": old_work_cooldown if reset_work else None,
                        "old_income_cooldown": old_income_cooldown if reset_income else None,
                        "is_vip": is_vip,
                        "vip_updated": vip_updated,
                        "user_obj": user_obj
                    })
                    
                except Exception as e:
                    # Rollback on error
                    conn.rollback()
                    print(f"Error resetting cooldowns for {user_id}: {e}")
                    failed_users.append((user_id, username, str(e)))
                
            except Exception as e:
                print(f"Error processing user {user_id}: {e}")
                failed_users.append((user_id, username, str(e)))
    
    # Create response embed
    if reset_users:
        embed = Embed(
            title="⏰ Bulk Cooldown Reset",
            description=f"Reset cooldowns for {len(reset_users)} users",
            color=bank.success_color
        )
        
        # Add details about which cooldowns were reset
        cooldowns_text = []
        if reset_work:
            cooldowns_text.append("Work cooldown")
        if reset_income:
            cooldowns_text.append("Income cooldown")
        
        embed.add_field(
            name="Reset Cooldowns",
            value="\n".join([f"• {c}" for c in cooldowns_text]),
            inline=False
        )
        
        # List the first 10 reset users
        display_users = reset_users[:10]
        user_list = []
        
        for user in display_users:
            user_text = f"**{user['username']}**"
            if user['is_vip']:
                user_text += " (VIP)"
            user_list.append(user_text)
        
        if len(reset_users) > 10:
            user_list.append(f"...and {len(reset_users) - 10} more users")
        
        embed.add_field(
            name="Users",
            value="\n".join(user_list),
            inline=False
        )
        
        if failed_users:
            failed_list = [f"**{username}** - {error}" for user_id, username, error in failed_users[:5]]
            if len(failed_users) > 5:
                failed_list.append(f"...and {len(failed_users) - 5} more users")
            
            embed.add_field(
                name="Failed Resets",
                value="\n".join(failed_list),
                inline=False
            )
        
        # Create log embed
        log_embed = Embed(
            title="⏰ Bulk Cooldown Reset",
            description=f"{interaction.user.mention} reset cooldowns for {len(reset_users)} users",
            color=bank.info_color,
            timestamp=datetime.now()
        )
        
        log_embed.add_field(
            name="Reset Cooldowns",
            value="\n".join([f"• {c}" for c in cooldowns_text]),
            inline=False
        )
        
        log_embed.add_field(
            name="User Count",
            value=f"**Reset:** {len(reset_users)}\n"
                  f"**Failed:** {len(failed_users)}",
            inline=False
        )
        
        log_embed.set_footer(text=f"Reset by: {interaction.user.id}")
        
        # Send to log channel
        await bank.log_to_channel(bot, log_embed)
        
        # Send notifications to users
        for user in reset_users:
            try:
                if user["user_obj"]:
                    notification = Embed(
                        title="⏰ Cooldowns Reset",
                        description="Good news! Your cooldowns have been reset by a bank master.",
                        color=bank.success_color
                    )
                    
                    # Add details about which cooldowns were reset
                    notification.add_field(
                        name="Reset Cooldowns",
                        value="\n".join([f"• {c}" for c in cooldowns_text]),
                        inline=False
                    )
                    
                    try:
                        await user["user_obj"].send(embed=notification)
                    except:
                        # Can't send DM, just continue
                        pass
            except Exception as notify_err:
                print(f"Error notifying user about cooldown reset: {notify_err}")
                # Continue even if notification fails
        
    else:
        embed = Embed(
            title="❌ Bulk Reset Failed",
            description="Failed to reset cooldowns for any users.",
            color=bank.error_color
        )
        
        if failed_users:
            failed_list = [f"**{username}** - {error}" for user_id, username, error in failed_users[:10]]
            if len(failed_users) > 10:
                failed_list.append(f"...and {len(failed_users) - 10} more users")
            
            embed.add_field(
                name="Errors",
                value="\n".join(failed_list),
                inline=False
            )
    
    await interaction.response.edit_message(embed=embed, view=None)

@bot.tree.command(name="get_logs", description="Get detailed transaction logs for a user with optional export")
@app_commands.guild_only()
@app_commands.describe(
    user="The user to get logs for",
    transaction_type="Type of transaction to filter by (optional)",
    sort_order="Sort order for transactions",
    date_range="Filter transactions by date range",
    limit="Number of logs to retrieve (default: 10, max: 50)",
    export="Export the logs as CSV file"
)
async def get_logs(
    interaction: discord.Interaction, 
    user: discord.Member,
    transaction_type: Optional[Literal["work", "income", "purchase", "admin", "vip_purchase"]] = None,
    sort_order: Optional[Literal["newest_first", "oldest_first"]] = "newest_first",
    date_range: Optional[Literal["today", "this_week", "this_month", "all_time"]] = "all_time",
    limit: Optional[int] = 10,
    export: Optional[bool] = False
):
    # Use deferred response to prevent timeouts
    await defer_response(interaction)
    
    # Check if user is bot owner or has the specific ID
    if interaction.user.id != bot.owner_id and interaction.user.id not in [1339963012945285192,1058171261232103424]:
        embed = Embed(
            title="❌ Access Denied",
            description="You don't have permission to use this command!",
            color=bank.error_color
        )
        await send_message(interaction, embed=embed, ephemeral=True)
        return
    
    # Limit the number of logs to retrieve (max 50)
    if limit > 50:
        limit = 50
    
    try:
        with bank.get_db_connection() as conn:
            c = conn.cursor()
            
            # Get current balance first
            c.execute('''
                SELECT (galleons * ? + sickles * ? + knuts) as total_knuts
                FROM users WHERE user_id = ?
            ''', (bank.KNUTS_PER_GALLEON, bank.KNUTS_PER_SICKLE, str(user.id)))
            
            current_balance_result = c.fetchone()
            current_balance = current_balance_result[0] if current_balance_result else 0
            
            # Build the query based on filters
            query = '''
                SELECT t.id, t.amount, t.type, t.timestamp, t.modifier_id, t.details
                FROM transactions t
                WHERE t.user_id = ?
            '''
            params = [str(user.id)]
            
            # Add transaction type filter if specified
            if transaction_type:
                query += " AND t.type = ?"
                params.append(transaction_type)
            
            # Add date filter
            if date_range == "today":
                query += " AND DATE(t.timestamp) = DATE('now')"
            elif date_range == "this_week":
                query += " AND DATE(t.timestamp) >= DATE('now', 'weekday 0', '-7 days')"
            elif date_range == "this_month":
                query += " AND DATE(t.timestamp) >= DATE('now', 'start of month')"
            
            # Sort order
            if sort_order == "newest_first":
                query += " ORDER BY t.timestamp DESC"
            else:
                query += " ORDER BY t.timestamp ASC"
            
            # Only add limit if not exporting
            if not export:
                query += " LIMIT ?"
                params.append(limit)
            
            c.execute(query, params)
            logs = c.fetchall()
            
            if not logs:
                embed = Embed(
                    title="📜 Transaction Logs",
                    description=f"No logs found for {user.mention}" + 
                               (f" with type '{transaction_type}'" if transaction_type else ""),
                    color=bank.info_color
                )
                await send_message(interaction, embed=embed)
                return
                
            # If exporting to CSV
            if export:
                # Calculate balances for each transaction
                transaction_balances = {}
                running_balance = current_balance
                logs_with_balance = []
                
                # If sorting newest first, we need to reverse to calculate balances
                logs_for_calculation = logs if sort_order == "oldest_first" else reversed(logs)
                
                # First pass: Calculate balances
                for log in logs_for_calculation:
                    log_id, amount, log_type, timestamp, modifier_id, details = log
                    
                    if sort_order == "oldest_first":
                        # For oldest first, we start from the beginning and add up
                        balance_before = running_balance
                        balance_after = balance_before + amount
                        running_balance = balance_after
                    else:
                        # For newest first, we're working backwards
                        balance_after = running_balance
                        balance_before = balance_after - amount
                        running_balance = balance_before
                    
                    # Store balances
                    transaction_balances[log_id] = {
                        'before': balance_before,
                        'after': balance_after
                    }
                
                # Create CSV data
                csv_data = "ID,Date,Type,Amount,Details,Balance Before,Balance After,Modified By\n"
                
                for log in logs:
                    log_id, amount, log_type, timestamp, modifier_id, details = log
                    
                    # Format timestamp
                    try:
                        dt = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                        formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        formatted_time = timestamp
                    
                    # Get balance info
                    balance_info = transaction_balances.get(log_id, {'before': 0, 'after': 0})
                    
                    # Use absolute amount for purchases
                    display_amount = abs(amount) if log_type in ["purchase", "vip_purchase"] else amount
                    
                    # Format CSV line
                    # Escape any commas in the details text
                    safe_details = f'"{details}"' if ',' in details else details
                    
                    csv_line = f"{log_id},{formatted_time},{log_type},{display_amount},{safe_details},{balance_info['before']},{balance_info['after']},{modifier_id}\n"
                    csv_data += csv_line
                
                # Create a discord file to send
                import io
                file_data = io.BytesIO(csv_data.encode('utf-8'))
                filename = f"bank_statement_{user.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                
                # Create summary embed
                embed = Embed(
                    title="🏦 Bank Statement Export",
                    description=f"Transaction history for {user.mention} has been exported to CSV.",
                    color=bank.info_color
                )
                
                embed.add_field(
                    name="Export Details",
                    value=f"**Transactions:** {len(logs)}\n"
                          f"**Date Range:** {date_range.replace('_', ' ').title()}\n"
                          f"**Sort Order:** {sort_order.replace('_', ' ').title()}\n"
                          f"**Current Balance:** {bank.format_currency(current_balance)}",
                    inline=False
                )
                
                # Send the file
                file = discord.File(file_data, filename=filename)
                
                # Using the low-level API directly here because our send_message helper doesn't support file attachments
                if not interaction.response.is_done():
                    await interaction.response.send_message(embed=embed, file=file)
                else:
                    await interaction.followup.send(embed=embed, file=file)
                
                return
                
            # Regular embed display (not exporting)
            # Create the embed
            embed = Embed(
                title="🏦 Bank Statement",
                description=f"Transaction history for {user.mention}" +
                           (f" (Type: {transaction_type})" if transaction_type else ""),
                color=bank.info_color
            )
            
            # Calculate balances for each transaction (starting from current and working backwards)
            transaction_balances = {}  # To store balance after each transaction
            running_balance = current_balance
            
            # For newest first (default), we start from current balance and work backwards
            if sort_order == "newest_first":
                # First pass: Calculate balances after each transaction
                for log in reversed(logs):  # Process from oldest to newest
                    log_id, amount, type, timestamp, modifier_id, details = log
                    
                    # Store the balance before this transaction
                    balance_before = running_balance - amount
                    
                    # Store balances for this transaction
                    transaction_balances[log_id] = {
                        'before': balance_before,
                        'after': running_balance
                    }
                    
                    # Update running balance for next (older) transaction
                    running_balance = balance_before
            else:
                # For oldest first, we calculate forward
                starting_balance = current_balance
                for log in logs:
                    log_id, amount, type, timestamp, modifier_id, details = log
                    # Find all transactions after this one
                    c.execute('''
                        SELECT SUM(amount) FROM transactions
                        WHERE user_id = ? AND timestamp > ?
                    ''', (str(user.id), timestamp))
                    result = c.fetchone()
                    later_sum = result[0] if result[0] is not None else 0
                    
                    # Calculate balance after this transaction
                    balance_after = current_balance - later_sum
                    balance_before = balance_after - amount
                    
                    transaction_balances[log_id] = {
                        'before': balance_before,
                        'after': balance_after
                    }
            
            # Add logs to the embed (in original order)
            for log in logs:
                log_id, amount, log_type, timestamp, modifier_id, details = log
                
                # Format the timestamp
                try:
                    # Convert SQLite timestamp to datetime object
                    dt = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                    formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    formatted_time = timestamp
                
                # Format the amount with color based on positive/negative and transaction type
                if log_type in ["purchase", "vip_purchase"]:
                    # For purchases, show the absolute value with a minus sign
                    display_amount = abs(amount)
                    amount_str = f"**-{bank.format_currency(display_amount)}**"
                elif amount >= 0:
                    amount_str = f"**+{bank.format_currency(amount)}**"
                else:
                    amount_str = f"**{bank.format_currency(amount)}**"
                
                # Get the balances
                balance_info = transaction_balances.get(log_id, {'before': 0, 'after': 0})
                
                # Create field for this log
                embed.add_field(
                    name=f"ID: {log_id} | {formatted_time}",
                    value=f"Type: `{log_type}`\nAmount: {amount_str}\nDetails: {details}\n"
                          f"Balance Before: {bank.format_currency(balance_info['before'])}\n"
                          f"Balance After: {bank.format_currency(balance_info['after'])}\n"
                          f"Modifier: <@{modifier_id}>",
                    inline=False
                )
            
            # Add current balance at the top
            embed.insert_field_at(
                0,
                name="Current Balance",
                value=bank.format_currency(current_balance),
                inline=False
            )
            
            await send_message(interaction, embed=embed)
            
    except Exception as e:
        print(f"Error retrieving logs: {e}")
        error_embed = Embed(
            title="❌ Error",
            description=f"An error occurred while retrieving logs: {str(e)}",
            color=bank.error_color
        )
        await send_message(interaction, embed=error_embed, ephemeral=True)

@bot.tree.command(name="sync_commands", description="Manually sync commands with Discord (Owner only)")
@app_commands.guild_only()
async def sync_commands(interaction: discord.Interaction):
    # Check if user is the bot owner
    application_info = await bot.application_info()
    if interaction.user.id != application_info.owner.id:
        embed = Embed(
            title="❌ Permission Denied",
            description="Only the bot owner can use this command.",
            color=bank.error_color
        )
        await send_message(interaction, embed=embed, ephemeral=True)
        return
    
    # Defer the response to prevent timeout
    await defer_response(interaction)
    
    try:
        # Sync commands
        await bot.tree.sync()
        
        embed = Embed(
            title="✅ Commands Synced",
            description="All commands have been synced with Discord successfully!",
            color=bank.success_color
        )
        await send_message(interaction, embed=embed)
        
    except Exception as e:
        logger.error(f"Error syncing commands: {e}")
        embed = Embed(
            title="❌ Sync Error",
            description=f"An error occurred while syncing commands: {e}",
            color=bank.error_color
        )
        await send_message(interaction, embed=embed, ephemeral=True)

@bot.tree.command(name="cleanup_notifications", description="Clean up notifications for users who left all servers (Owner only)")
@app_commands.guild_only()
async def cleanup_notifications(interaction: discord.Interaction):
    # Check if user is the bot owner
    application_info = await bot.application_info()
    if interaction.user.id != application_info.owner.id:
        embed = Embed(
            title="❌ Access Denied",
            description="Only the bot owner can use this command!",
            color=bank.error_color
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return

    try:
        # Defer response to prevent timeout
        await interaction.response.defer(ephemeral=True)

        # Clean up notifications for missing users
        cleaned_count = bank.cleanup_notifications_for_missing_users(bot)

        embed = Embed(
            title="🧹 Notification Cleanup Complete",
            description=f"Cleaned up {cleaned_count} notifications for users who are no longer in any mutual servers.",
            color=bank.success_color
        )

        if cleaned_count > 0:
            embed.add_field(
                name="Details",
                value="These users had left all servers where the bot is present, so their notifications were removed to prevent errors.",
                inline=False
            )

        await interaction.followup.send(embed=embed, ephemeral=True)

    except Exception as e:
        embed = Embed(
            title="❌ Cleanup Failed",
            description=f"Error cleaning up notifications: {str(e)}",
            color=bank.error_color
        )
        await interaction.followup.send(embed=embed, ephemeral=True)

# Replace this last line with token load
bot.run(config['token'])

